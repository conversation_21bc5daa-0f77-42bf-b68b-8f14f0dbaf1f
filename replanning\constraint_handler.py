"""
约束处理器

确保重规划结果的可行性，处理硬约束和软约束
基于现有资源约束和时间约束进行验证和修复
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any, Callable
import logging


class ConstraintHandler:
    """
    约束处理器
    
    功能:
    1. 硬约束验证（资源容量、时间窗口）
    2. 软约束处理（负载均衡、优先级）
    3. 不可行解修复
    4. 约束违反惩罚计算
    """
    
    def __init__(self, config: Dict[str, Any]):
        """
        初始化约束处理器
        
        Args:
            config: 配置参数字典
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 硬约束列表
        self.hard_constraints = []
        
        # 软约束列表
        self.soft_constraints = []
        
        # 约束权重
        self.constraint_weights = {}
        
        # 初始化约束
        self._initialize_constraints()
        
        # 约束违反统计
        self.violation_statistics = {
            'total_violations': 0,
            'violation_types': {},
            'repair_success_rate': 0.0
        }
        
        self.logger.info("ConstraintHandler initialized")
    
    def _initialize_constraints(self):
        """初始化约束条件"""
        # 添加卫星容量约束
        self.add_satellite_capacity_constraints()
        
        # 添加时间约束
        self.add_temporal_constraints()
        
        # 添加访问约束
        self.add_access_constraints()
        
        # 添加软约束
        self.add_load_balance_constraint()
        self.add_priority_constraint()
    
    def add_satellite_capacity_constraints(self):
        """添加卫星容量约束"""
        def satellite_capacity_constraint(solution: Dict[str, Any]) -> bool:
            """检查卫星容量约束"""
            try:
                num_satellites = self.config.get('num_satellites', 3)
                memory_total = self.config.get('memory_total', 0.3)
                power_total = self.config.get('power_total', 5.0)
                
                satellite_indices = solution.get('satellite_indices')
                if satellite_indices is None:
                    return True
                
                # 获取任务资源需求（从静态数据中）
                static_data = solution.get('static_data')
                if static_data is None:
                    return True
                
                # 检查每颗卫星的资源约束
                for sat_idx in range(num_satellites):
                    # 找到分配给该卫星的任务
                    sat_tasks = (satellite_indices == sat_idx).nonzero(as_tuple=True)[0]
                    
                    if len(sat_tasks) == 0:
                        continue
                    
                    # 计算总内存和功率需求
                    # static_data[:, 5, :]: memory consumption
                    # static_data[:, 6, :]: power consumption
                    total_memory = torch.sum(static_data[:, 5, sat_tasks]).item()
                    total_power = torch.sum(static_data[:, 6, sat_tasks]).item()
                    
                    if total_memory > memory_total or total_power > power_total:
                        return False
                
                return True
                
            except Exception as e:
                self.logger.error(f"Capacity constraint check failed: {e}")
                return True  # 保守处理，假设满足约束
        
        self.hard_constraints.append(satellite_capacity_constraint)
        self.logger.debug("Added satellite capacity constraints")
    
    def add_temporal_constraints(self):
        """添加时间约束"""
        def temporal_constraint(solution: Dict[str, Any]) -> bool:
            """检查时间约束"""
            try:
                tour_indices = solution.get('tour_indices')
                satellite_indices = solution.get('satellite_indices')
                static_data = solution.get('static_data')

                if any(x is None for x in [tour_indices, satellite_indices, static_data]):
                    return True

                # 简化的时间约束检查
                # 只检查基本的时间窗口约束
                if len(tour_indices) == 0:
                    return True

                # 检查任务索引是否在有效范围内
                max_task_idx = static_data.size(2) - 1
                if len(tour_indices) > 0 and torch.max(tour_indices) > max_task_idx:
                    return False

                # 简化的时间窗口检查 - 只检查基本有效性
                # 在实际应用中，这里应该有更复杂的时间约束检查
                return True

                return True

            except Exception as e:
                self.logger.error(f"Temporal constraint check failed: {e}")
                return True
        
        self.hard_constraints.append(temporal_constraint)
        self.logger.debug("Added temporal constraints")
    
    def add_access_constraints(self):
        """添加访问约束"""
        def access_constraint(solution: Dict[str, Any]) -> bool:
            """检查访问约束"""
            try:
                tour_indices = solution.get('tour_indices')
                satellite_indices = solution.get('satellite_indices')
                dynamic_data = solution.get('dynamic_data')
                
                if any(x is None for x in [tour_indices, satellite_indices, dynamic_data]):
                    return True
                
                # 检查每个任务是否可以被分配的卫星访问
                # dynamic_data[:, 1, :, :]: access marks
                
                for i, (task_idx, sat_idx) in enumerate(zip(tour_indices, satellite_indices)):
                    if task_idx < dynamic_data.size(2) and sat_idx < dynamic_data.size(3):
                        access_mark = dynamic_data[:, 1, task_idx, sat_idx]
                        if torch.sum(access_mark) == 0:
                            return False  # 卫星无法访问该任务
                
                return True
                
            except Exception as e:
                self.logger.error(f"Access constraint check failed: {e}")
                return True
        
        self.hard_constraints.append(access_constraint)
        self.logger.debug("Added access constraints")
    
    def add_load_balance_constraint(self):
        """添加负载均衡软约束"""
        def load_balance_constraint(solution: Dict[str, Any]) -> float:
            """计算负载不均衡惩罚"""
            try:
                satellite_indices = solution.get('satellite_indices')
                if satellite_indices is None:
                    return 0.0
                
                num_satellites = self.config.get('num_satellites', 3)
                
                # 统计每颗卫星的任务数量
                task_counts = torch.zeros(num_satellites)
                for sat_idx in range(num_satellites):
                    task_counts[sat_idx] = torch.sum(satellite_indices == sat_idx)
                
                # 计算负载方差作为惩罚
                load_variance = torch.var(task_counts).item()
                max_variance = (len(task_counts) ** 2) / 4  # 理论最大方差
                
                # 归一化惩罚
                penalty = load_variance / max_variance if max_variance > 0 else 0.0
                return penalty
                
            except Exception as e:
                self.logger.error(f"Load balance constraint check failed: {e}")
                return 0.0
        
        self.soft_constraints.append(load_balance_constraint)
        self.constraint_weights[load_balance_constraint] = 0.3
        self.logger.debug("Added load balance soft constraint")
    
    def add_priority_constraint(self):
        """添加优先级软约束"""
        def priority_constraint(solution: Dict[str, Any]) -> float:
            """计算优先级违反惩罚"""
            try:
                tour_indices = solution.get('tour_indices')
                static_data = solution.get('static_data')
                
                if tour_indices is None or static_data is None:
                    return 0.0
                
                # 假设收益代表优先级（收益越高优先级越高）
                # static_data[:, 4, :]: revenue
                selected_revenues = static_data[:, 4, tour_indices]
                total_revenue = torch.sum(static_data[:, 4, :])
                
                if total_revenue > 0:
                    # 计算选中任务的收益比例
                    selected_ratio = torch.sum(selected_revenues) / total_revenue
                    # 惩罚低收益选择
                    penalty = max(0, 0.8 - selected_ratio.item())  # 期望至少选择80%的收益
                    return penalty
                
                return 0.0
                
            except Exception as e:
                self.logger.error(f"Priority constraint check failed: {e}")
                return 0.0
        
        self.soft_constraints.append(priority_constraint)
        self.constraint_weights[priority_constraint] = 0.2
        self.logger.debug("Added priority soft constraint")
    
    def validate_solution(self, solution: Dict[str, Any]) -> Tuple[bool, float]:
        """
        验证解的可行性
        
        Args:
            solution: 解决方案字典
            
        Returns:
            Tuple: (是否可行, 软约束惩罚)
        """
        try:
            # 检查硬约束
            for constraint in self.hard_constraints:
                if not constraint(solution):
                    self.violation_statistics['total_violations'] += 1
                    constraint_name = constraint.__name__
                    if constraint_name not in self.violation_statistics['violation_types']:
                        self.violation_statistics['violation_types'][constraint_name] = 0
                    self.violation_statistics['violation_types'][constraint_name] += 1
                    
                    return False, float('inf')  # 硬约束违反，返回无穷大惩罚
            
            # 计算软约束惩罚
            total_penalty = 0.0
            for constraint in self.soft_constraints:
                penalty = constraint(solution)
                weight = self.constraint_weights.get(constraint, 1.0)
                total_penalty += weight * penalty
            
            return True, total_penalty
            
        except Exception as e:
            self.logger.error(f"Solution validation failed: {e}")
            return False, float('inf')
    
    def repair_solution(self, invalid_solution: Dict[str, Any]) -> Dict[str, Any]:
        """
        修复不可行解
        
        Args:
            invalid_solution: 不可行解
            
        Returns:
            Dict: 修复后的解
        """
        try:
            repaired_solution = invalid_solution.copy()
            
            # 修复容量约束违反
            repaired_solution = self._repair_capacity_violations(repaired_solution)
            
            # 修复时间约束违反
            repaired_solution = self._repair_temporal_violations(repaired_solution)
            
            # 修复访问约束违反
            repaired_solution = self._repair_access_violations(repaired_solution)
            
            # 验证修复结果
            is_valid, penalty = self.validate_solution(repaired_solution)
            
            if is_valid:
                self.violation_statistics['repair_success_rate'] = \
                    (self.violation_statistics['repair_success_rate'] * 0.9 + 1.0 * 0.1)
            else:
                self.violation_statistics['repair_success_rate'] = \
                    (self.violation_statistics['repair_success_rate'] * 0.9 + 0.0 * 0.1)
            
            repaired_solution['repaired'] = True
            repaired_solution['repair_penalty'] = penalty
            
            return repaired_solution
            
        except Exception as e:
            self.logger.error(f"Solution repair failed: {e}")
            return invalid_solution
    
    def _repair_capacity_violations(self, solution: Dict[str, Any]) -> Dict[str, Any]:
        """修复容量约束违反"""
        try:
            satellite_indices = solution.get('satellite_indices')
            tour_indices = solution.get('tour_indices')
            static_data = solution.get('static_data')
            
            if any(x is None for x in [satellite_indices, tour_indices, static_data]):
                return solution
            
            num_satellites = self.config.get('num_satellites', 3)
            memory_total = self.config.get('memory_total', 0.3)
            power_total = self.config.get('power_total', 5.0)
            
            # 为每颗卫星修复容量违反
            for sat_idx in range(num_satellites):
                sat_task_mask = (satellite_indices == sat_idx)
                sat_tasks = tour_indices[sat_task_mask]
                
                if len(sat_tasks) == 0:
                    continue
                
                # 计算资源需求
                memory_demands = static_data[:, 5, sat_tasks].flatten()
                power_demands = static_data[:, 6, sat_tasks].flatten()
                revenues = static_data[:, 4, sat_tasks].flatten()
                
                # 按收益排序（优先保留高收益任务）
                sorted_indices = torch.argsort(revenues, descending=True)
                
                # 贪心选择任务直到满足约束
                selected_tasks = []
                total_memory = 0
                total_power = 0
                
                for idx in sorted_indices:
                    task_memory = memory_demands[idx].item()
                    task_power = power_demands[idx].item()
                    
                    if (total_memory + task_memory <= memory_total and
                        total_power + task_power <= power_total):
                        selected_tasks.append(sat_tasks[idx])
                        total_memory += task_memory
                        total_power += task_power
                
                # 更新解
                # 移除该卫星的所有任务
                keep_mask = (satellite_indices != sat_idx)
                solution['tour_indices'] = tour_indices[keep_mask]
                solution['satellite_indices'] = satellite_indices[keep_mask]
                
                # 添加修复后的任务
                if selected_tasks:
                    selected_tensor = torch.tensor(selected_tasks)
                    sat_tensor = torch.full((len(selected_tasks),), sat_idx, dtype=satellite_indices.dtype)
                    
                    solution['tour_indices'] = torch.cat([solution['tour_indices'], selected_tensor])
                    solution['satellite_indices'] = torch.cat([solution['satellite_indices'], sat_tensor])
            
            return solution
            
        except Exception as e:
            self.logger.error(f"Capacity repair failed: {e}")
            return solution
    
    def _repair_temporal_violations(self, solution: Dict[str, Any]) -> Dict[str, Any]:
        """修复时间约束违反"""
        # 简化实现，实际应用中需要更复杂的时间调度算法
        return solution
    
    def _repair_access_violations(self, solution: Dict[str, Any]) -> Dict[str, Any]:
        """修复访问约束违反"""
        try:
            tour_indices = solution.get('tour_indices')
            satellite_indices = solution.get('satellite_indices')
            dynamic_data = solution.get('dynamic_data')
            
            if any(x is None for x in [tour_indices, satellite_indices, dynamic_data]):
                return solution
            
            # 检查并修复访问违反
            valid_assignments = []
            
            for i, (task_idx, sat_idx) in enumerate(zip(tour_indices, satellite_indices)):
                if (task_idx < dynamic_data.size(2) and sat_idx < dynamic_data.size(3)):
                    access_mark = dynamic_data[:, 1, task_idx, sat_idx]
                    
                    if torch.sum(access_mark) > 0:
                        # 可以访问，保留
                        valid_assignments.append((task_idx, sat_idx))
                    else:
                        # 无法访问，寻找可访问的卫星
                        for alt_sat in range(dynamic_data.size(3)):
                            alt_access = dynamic_data[:, 1, task_idx, alt_sat]
                            if torch.sum(alt_access) > 0:
                                valid_assignments.append((task_idx, alt_sat))
                                break
            
            # 更新解
            if valid_assignments:
                new_tour_indices = torch.tensor([t[0] for t in valid_assignments])
                new_satellite_indices = torch.tensor([t[1] for t in valid_assignments])
                
                solution['tour_indices'] = new_tour_indices
                solution['satellite_indices'] = new_satellite_indices
            
            return solution
            
        except Exception as e:
            self.logger.error(f"Access repair failed: {e}")
            return solution
    
    def get_violation_statistics(self) -> Dict[str, Any]:
        """获取约束违反统计信息"""
        return self.violation_statistics.copy()

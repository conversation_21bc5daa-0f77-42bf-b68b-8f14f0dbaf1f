"""
星座重规划触发系统

实现多层次触发机制，基于现有GPNConstellation架构
集成性能监控、环境变化检测、资源约束等触发条件
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging
import time

# 导入现有模块
from constellation_smp.constellation_smp import reward as constellation_smp_reward


class ConstellationReplanningTrigger:
    """
    星座重规划触发器
    
    功能:
    1. 性能下降触发
    2. 环境变化触发
    3. 资源约束触发
    4. 紧急事件触发
    5. 时间窗口触发
    """
    
    def __init__(self, constellation_model, config: Dict[str, Any]):
        """
        初始化触发器
        
        Args:
            constellation_model: GPNConstellation模型
            config: 配置参数
        """
        self.constellation_model = constellation_model
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # 触发器组件
        self.triggers = {
            'performance': PerformanceTrigger(config, constellation_model),
            'environment': EnvironmentTrigger(config),
            'resource': ResourceTrigger(config),
            'emergency': EmergencyTrigger(config),
            'temporal': TemporalTrigger(config)
        }
        
        # 触发历史
        self.trigger_history = []
        self.max_history_length = 1000
        
        # 全局触发阈值
        self.global_threshold = config.get('global_trigger_threshold', 0.5)
        
        self.logger.info("ConstellationReplanningTrigger initialized")
    
    def check_replanning_need(self, current_state: Dict[str, Any], 
                            dynamic_state: torch.Tensor) -> Dict[str, float]:
        """
        检查是否需要重规划
        
        Args:
            current_state: 当前系统状态
            dynamic_state: 动态状态数据
            
        Returns:
            Dict: 触发信号字典 {trigger_type: signal_strength}
        """
        trigger_signals = {}
        
        try:
            # 检查各类触发条件
            for trigger_name, trigger in self.triggers.items():
                signal_strength = trigger.evaluate(current_state, dynamic_state)
                
                if signal_strength > trigger.threshold:
                    trigger_signals[trigger_name] = signal_strength
                    self.logger.debug(f"{trigger_name} trigger activated: {signal_strength:.4f}")
            
            # 记录触发历史
            self._record_trigger_event(trigger_signals)
            
            # 应用全局阈值过滤
            filtered_signals = self._apply_global_filter(trigger_signals)
            
            return filtered_signals
            
        except Exception as e:
            self.logger.error(f"Error in trigger evaluation: {e}")
            return {}
    
    def _apply_global_filter(self, trigger_signals: Dict[str, float]) -> Dict[str, float]:
        """应用全局阈值过滤"""
        filtered = {}
        
        for trigger_type, strength in trigger_signals.items():
            if strength >= self.global_threshold:
                filtered[trigger_type] = strength
        
        return filtered
    
    def _record_trigger_event(self, trigger_signals: Dict[str, float]):
        """记录触发事件"""
        event = {
            'timestamp': time.time(),
            'triggers': trigger_signals.copy(),
            'trigger_count': len(trigger_signals)
        }
        
        self.trigger_history.append(event)
        
        # 限制历史长度
        if len(self.trigger_history) > self.max_history_length:
            self.trigger_history.pop(0)
    
    def get_trigger_statistics(self) -> Dict[str, Any]:
        """获取触发统计信息"""
        if not self.trigger_history:
            return {'status': 'No trigger events recorded'}
        
        # 统计各类触发器的激活次数
        trigger_counts = {}
        total_events = len(self.trigger_history)
        
        for event in self.trigger_history:
            for trigger_type in event['triggers'].keys():
                if trigger_type not in trigger_counts:
                    trigger_counts[trigger_type] = 0
                trigger_counts[trigger_type] += 1
        
        # 计算触发频率
        trigger_frequencies = {
            trigger_type: count / total_events 
            for trigger_type, count in trigger_counts.items()
        }
        
        return {
            'total_events': total_events,
            'trigger_counts': trigger_counts,
            'trigger_frequencies': trigger_frequencies,
            'most_frequent_trigger': max(trigger_frequencies.items(), key=lambda x: x[1])[0] if trigger_frequencies else None
        }


class PerformanceTrigger:
    """性能下降触发器"""

    def __init__(self, config: Dict[str, Any], constellation_model=None):
        self.threshold = config.get('performance_threshold', 0.15)  # 15%性能下降
        self.baseline_window = config.get('baseline_window', 100)
        self.performance_history = []
        self.constellation_model = constellation_model
        self.logger = logging.getLogger(__name__)
    
    def evaluate(self, current_state: Dict[str, Any], dynamic_state: torch.Tensor) -> float:
        """评估性能下降程度"""
        try:
            # 计算当前性能
            static = current_state['static']
            
            # 使用现有模型生成当前规划
            if self.constellation_model is not None:
                with torch.no_grad():
                    tour_indices, satellite_indices, _, _ = \
                        self.constellation_model(static, dynamic_state)
            else:
                # 如果没有模型，返回0（无性能下降）
                return 0.0
            
            if tour_indices is None:
                return 0.0
            
            # 计算当前奖励
            current_reward, _, _, _, _ = constellation_smp_reward(
                static, tour_indices, satellite_indices, 'hybrid'
            )
            
            current_performance = current_reward.mean().item()
            self.performance_history.append(current_performance)
            
            # 与历史基线比较
            if len(self.performance_history) >= self.baseline_window:
                baseline_performance = np.mean(
                    self.performance_history[-self.baseline_window:]
                )
                
                if baseline_performance > 0:
                    performance_drop = (baseline_performance - current_performance) / baseline_performance
                    return max(0, performance_drop - self.threshold)
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Performance evaluation error: {e}")
            return 0.0


class EnvironmentTrigger:
    """环境变化触发器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.threshold = config.get('environment_threshold', 0.2)
        self.change_sensitivity = config.get('change_sensitivity', 1.0)
        self.logger = logging.getLogger(__name__)
    
    def evaluate(self, current_state: Dict[str, Any], dynamic_state: torch.Tensor) -> float:
        """评估环境变化程度"""
        try:
            changes = current_state.get('changes', {})
            
            if not changes:
                return 0.0
            
            # 计算变化强度
            change_magnitude = sum(changes.values()) * self.change_sensitivity
            
            # 归一化到[0,1]范围
            normalized_magnitude = min(change_magnitude, 1.0)
            
            return normalized_magnitude if normalized_magnitude > self.threshold else 0.0
            
        except Exception as e:
            self.logger.error(f"Environment evaluation error: {e}")
            return 0.0


class ResourceTrigger:
    """资源约束触发器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.threshold = config.get('resource_threshold', 0.8)  # 80%资源使用率
        self.critical_threshold = config.get('critical_resource_threshold', 0.95)
        self.logger = logging.getLogger(__name__)
    
    def evaluate(self, current_state: Dict[str, Any], dynamic_state: torch.Tensor) -> float:
        """评估资源约束程度"""
        try:
            # 基于dynamic_state计算资源使用率
            # dynamic_state[:, 2, :, :]: memory surplus
            # dynamic_state[:, 3, :, :]: power surplus
            
            memory_usage = 1.0 - torch.mean(dynamic_state[:, 2, :, :])
            power_usage = 1.0 - torch.mean(dynamic_state[:, 3, :, :])
            
            max_usage = max(memory_usage.item(), power_usage.item())
            
            if max_usage > self.critical_threshold:
                return 1.0  # 临界资源约束
            elif max_usage > self.threshold:
                return (max_usage - self.threshold) / (self.critical_threshold - self.threshold)
            else:
                return 0.0
                
        except Exception as e:
            self.logger.error(f"Resource evaluation error: {e}")
            return 0.0


class EmergencyTrigger:
    """紧急事件触发器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.threshold = config.get('emergency_threshold', 0.1)  # 低阈值，高敏感度
        self.emergency_indicators = config.get('emergency_indicators', [])
        self.logger = logging.getLogger(__name__)
    
    def evaluate(self, current_state: Dict[str, Any], dynamic_state: torch.Tensor) -> float:
        """评估紧急事件"""
        try:
            # 检查紧急指标
            emergency_score = 0.0
            
            # 检查卫星故障（访问标记突然变为0）
            access_marks = dynamic_state[:, 1, :, :]
            if torch.sum(access_marks) == 0:
                emergency_score = 1.0  # 完全失去访问能力
            
            # 检查资源耗尽
            memory_surplus = dynamic_state[:, 2, :, :]
            power_surplus = dynamic_state[:, 3, :, :]
            
            if torch.min(memory_surplus) <= 0 or torch.min(power_surplus) <= 0:
                emergency_score = max(emergency_score, 0.8)  # 资源耗尽
            
            return emergency_score
            
        except Exception as e:
            self.logger.error(f"Emergency evaluation error: {e}")
            return 0.0


class TemporalTrigger:
    """时间窗口触发器"""
    
    def __init__(self, config: Dict[str, Any]):
        self.threshold = config.get('temporal_threshold', 0.3)
        self.window_sensitivity = config.get('window_sensitivity', 1.0)
        self.logger = logging.getLogger(__name__)
    
    def evaluate(self, current_state: Dict[str, Any], dynamic_state: torch.Tensor) -> float:
        """评估时间窗口变化"""
        try:
            # 检查时间窗口变化
            time_windows = dynamic_state[:, 0, :, :]
            
            # 计算活跃时间窗口比例
            active_ratio = torch.mean(time_windows).item()
            
            # 如果活跃时间窗口过少，触发重规划
            if active_ratio < self.threshold:
                return (self.threshold - active_ratio) / self.threshold * self.window_sensitivity
            
            return 0.0
            
        except Exception as e:
            self.logger.error(f"Temporal evaluation error: {e}")
            return 0.0

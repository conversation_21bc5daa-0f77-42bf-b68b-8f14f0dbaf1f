"""
重规划系统使用示例

演示如何使用动态重规划系统进行卫星星座任务规划
包括基本使用、高级配置和性能监控
"""

import torch
import numpy as np
import logging
import time
from typing import Dict, Any

# 导入现有模块
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.constellation_smp import ConstellationSMPDataset
from hyperparameter import args

# 导入重规划模块
from replanning.constellation_replanning_engine import ConstellationReplanningEngine

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class ReplanningDemo:
    """重规划系统演示类"""
    
    def __init__(self):
        """初始化演示环境"""
        self.model = None
        self.replanning_engine = None
        self.test_data = None
        
        # 配置参数
        self.config = {
            'maml_inner_lr': 0.01,
            'maml_outer_lr': 0.001,
            'adaptation_steps': 3,
            'performance_threshold': 0.15,
            'environment_threshold': 0.2,
            'resource_threshold': 0.8,
            'emergency_threshold': 0.1,
            'temporal_threshold': 0.3,
            'global_trigger_threshold': 0.5,
            'num_satellites': 3,
            'memory_total': 0.3,
            'power_total': 5.0
        }
        
        logger.info("ReplanningDemo initialized")
    
    def setup_system(self):
        """设置重规划系统"""
        logger.info("Setting up replanning system...")
        
        try:
            # 1. 创建数据集实例以获取必要的函数
            temp_dataset = ConstellationSMPDataset(
                size=10, num_samples=1, seed=12345,
                memory_total=0.3, power_total=5.0, num_satellites=3
            )

            # 2. 创建基础模型
            self.model = GPNConstellation(
                static_size=9,
                dynamic_size=7,
                hidden_size=256,
                num_satellites=3,
                rnn='indrnn',
                num_layers=2,
                update_fn=temp_dataset.update_dynamic,
                mask_fn=temp_dataset.update_mask,
                num_nodes=50,
                dropout=0.1,
                constellation_mode='hybrid',
                use_transformer=True
            ).to(device)
            
            logger.info(f"Base model created with {sum(p.numel() for p in self.model.parameters())} parameters")
            
            # 2. 创建重规划引擎
            self.replanning_engine = ConstellationReplanningEngine(
                self.model, 
                self.config
            )
            
            logger.info("Replanning engine created")
            
            # 3. 创建测试数据
            self.test_data = self._create_test_scenarios()
            
            logger.info("Test scenarios created")
            
        except Exception as e:
            logger.error(f"System setup failed: {e}")
            raise
    
    def _create_test_scenarios(self) -> Dict[str, Any]:
        """创建测试场景"""
        scenarios = {}
        
        # 正常场景
        dataset_normal = ConstellationSMPDataset(
            size=50, num_samples=5, seed=12345,
            memory_total=0.3, power_total=5.0, num_satellites=3
        )
        static_normal, dynamic_normal, _ = dataset_normal[0]
        scenarios['normal'] = {
            'static': static_normal.unsqueeze(0).to(device),
            'dynamic': dynamic_normal.unsqueeze(0).to(device),
            'description': 'Normal operation scenario'
        }
        
        # 资源约束场景
        static_constrained = static_normal.clone()
        dynamic_constrained = dynamic_normal.clone()

        # 检查动态数据的维度并相应调整
        if len(dynamic_constrained.shape) == 3:  # (dynamic_size, seq_len, num_satellites)
            dynamic_constrained[2, :, :] *= 0.5  # 减少内存资源
            dynamic_constrained[3, :, :] *= 0.6  # 减少功率资源
        else:  # (batch_size, dynamic_size, seq_len, num_satellites)
            dynamic_constrained[:, 2, :, :] *= 0.5  # 减少内存资源
            dynamic_constrained[:, 3, :, :] *= 0.6  # 减少功率资源

        scenarios['resource_constrained'] = {
            'static': static_constrained.unsqueeze(0).to(device),
            'dynamic': dynamic_constrained.unsqueeze(0).to(device),
            'description': 'Resource constrained scenario'
        }
        
        # 卫星故障场景
        static_failure = static_normal.clone()
        dynamic_failure = dynamic_normal.clone()

        # 检查动态数据的维度并相应调整
        if len(dynamic_failure.shape) == 3:  # (dynamic_size, seq_len, num_satellites)
            dynamic_failure[1, :, 0] = 0  # 第一颗卫星失去访问能力
        else:  # (batch_size, dynamic_size, seq_len, num_satellites)
            dynamic_failure[:, 1, :, 0] = 0  # 第一颗卫星失去访问能力

        scenarios['satellite_failure'] = {
            'static': static_failure.unsqueeze(0).to(device),
            'dynamic': dynamic_failure.unsqueeze(0).to(device),
            'description': 'Satellite failure scenario'
        }
        
        # 时间窗口变化场景
        static_temporal = static_normal.clone()
        dynamic_temporal = dynamic_normal.clone()

        # 检查动态数据的维度并相应调整
        if len(dynamic_temporal.shape) == 3:  # (dynamic_size, seq_len, num_satellites)
            dynamic_temporal[0, :, :] *= 0.7  # 减少时间窗口
        else:  # (batch_size, dynamic_size, seq_len, num_satellites)
            dynamic_temporal[:, 0, :, :] *= 0.7  # 减少时间窗口

        scenarios['temporal_change'] = {
            'static': static_temporal.unsqueeze(0).to(device),
            'dynamic': dynamic_temporal.unsqueeze(0).to(device),
            'description': 'Temporal window change scenario'
        }
        
        return scenarios
    
    def demonstrate_basic_usage(self):
        """演示基本使用方法"""
        logger.info("\n" + "="*50)
        logger.info("BASIC USAGE DEMONSTRATION")
        logger.info("="*50)
        
        scenario = self.test_data['normal']
        static = scenario['static']
        dynamic = scenario['dynamic']
        
        logger.info(f"Testing scenario: {scenario['description']}")
        logger.info(f"Input shapes - Static: {static.shape}, Dynamic: {dynamic.shape}")
        
        # 执行重规划
        start_time = time.time()
        result = self.replanning_engine.execute_replanning_cycle(static, dynamic)
        end_time = time.time()
        
        # 显示结果
        logger.info(f"Replanning completed in {end_time - start_time:.3f} seconds")
        logger.info(f"Replanning triggered: {result.get('replanning_triggered', False)}")
        logger.info(f"Reward: {result.get('reward', 'N/A')}")
        logger.info(f"Revenue rate: {result.get('revenue_rate', 'N/A')}")
        
        return result
    
    def demonstrate_scenario_testing(self):
        """演示多场景测试"""
        logger.info("\n" + "="*50)
        logger.info("MULTI-SCENARIO TESTING")
        logger.info("="*50)
        
        results = {}
        
        for scenario_name, scenario_data in self.test_data.items():
            logger.info(f"\nTesting {scenario_name}: {scenario_data['description']}")
            
            start_time = time.time()
            result = self.replanning_engine.execute_replanning_cycle(
                scenario_data['static'], 
                scenario_data['dynamic']
            )
            end_time = time.time()
            
            response_time = end_time - start_time
            results[scenario_name] = {
                'response_time': response_time,
                'replanning_triggered': result.get('replanning_triggered', False),
                'reward': result.get('reward', 0),
                'result': result
            }
            
            logger.info(f"  Response time: {response_time:.3f}s")
            logger.info(f"  Replanning triggered: {result.get('replanning_triggered', False)}")
            logger.info(f"  Reward: {result.get('reward', 'N/A')}")
        
        return results
    
    def demonstrate_performance_monitoring(self):
        """演示性能监控"""
        logger.info("\n" + "="*50)
        logger.info("PERFORMANCE MONITORING")
        logger.info("="*50)
        
        # 运行多次重规划以收集统计数据
        logger.info("Running multiple replanning cycles for statistics...")
        
        for i in range(5):
            scenario_name = list(self.test_data.keys())[i % len(self.test_data)]
            scenario = self.test_data[scenario_name]
            
            result = self.replanning_engine.execute_replanning_cycle(
                scenario['static'], 
                scenario['dynamic']
            )
            
            logger.info(f"Cycle {i+1}: {scenario_name} - "
                       f"Triggered: {result.get('replanning_triggered', False)}")
        
        # 获取性能摘要
        performance_summary = self.replanning_engine.get_performance_summary()
        logger.info("\nPerformance Summary:")
        for key, value in performance_summary.items():
            logger.info(f"  {key}: {value}")
        
        # 获取各组件统计
        logger.info("\nComponent Statistics:")
        
        # 触发器统计
        trigger_stats = self.replanning_engine.trigger_manager.get_trigger_statistics()
        logger.info(f"Trigger statistics: {trigger_stats}")
        
        # 状态监控统计
        monitor_stats = self.replanning_engine.state_monitor.get_change_statistics()
        logger.info(f"State monitor statistics: {monitor_stats}")
        
        # MAML统计
        maml_stats = self.replanning_engine.maml_learner.get_adaptation_statistics()
        logger.info(f"MAML statistics: {maml_stats}")
        
        return performance_summary
    
    def demonstrate_advanced_configuration(self):
        """演示高级配置"""
        logger.info("\n" + "="*50)
        logger.info("ADVANCED CONFIGURATION")
        logger.info("="*50)
        
        # 调整触发阈值
        logger.info("Adjusting trigger thresholds...")
        
        # 降低性能阈值，使系统更敏感
        original_threshold = self.replanning_engine.trigger_manager.triggers['performance'].threshold
        self.replanning_engine.trigger_manager.triggers['performance'].threshold = 0.05
        
        logger.info(f"Performance threshold changed: {original_threshold} -> 0.05")
        
        # 测试敏感度变化
        scenario = self.test_data['normal']
        result_sensitive = self.replanning_engine.execute_replanning_cycle(
            scenario['static'], 
            scenario['dynamic']
        )
        
        logger.info(f"With sensitive threshold - Triggered: {result_sensitive.get('replanning_triggered', False)}")
        
        # 恢复原始阈值
        self.replanning_engine.trigger_manager.triggers['performance'].threshold = original_threshold
        
        # 调整MAML参数
        logger.info("\nAdjusting MAML parameters...")
        
        original_steps = self.config['adaptation_steps']
        self.config['adaptation_steps'] = 5
        
        logger.info(f"Adaptation steps changed: {original_steps} -> 5")
        
        # 测试适应步数变化
        result_more_steps = self.replanning_engine.execute_replanning_cycle(
            scenario['static'], 
            scenario['dynamic']
        )
        
        logger.info(f"With more adaptation steps - Triggered: {result_more_steps.get('replanning_triggered', False)}")
        
        # 恢复原始设置
        self.config['adaptation_steps'] = original_steps
    
    def run_complete_demo(self):
        """运行完整演示"""
        logger.info("🚀 Starting Constellation Replanning System Demo")
        
        try:
            # 设置系统
            self.setup_system()
            
            # 基本使用演示
            self.demonstrate_basic_usage()
            
            # 多场景测试
            scenario_results = self.demonstrate_scenario_testing()
            
            # 性能监控
            performance_summary = self.demonstrate_performance_monitoring()
            
            # 高级配置
            self.demonstrate_advanced_configuration()
            
            # 总结
            logger.info("\n" + "="*50)
            logger.info("DEMO SUMMARY")
            logger.info("="*50)
            
            logger.info("✅ System setup completed successfully")
            logger.info("✅ Basic usage demonstrated")
            logger.info("✅ Multi-scenario testing completed")
            logger.info("✅ Performance monitoring demonstrated")
            logger.info("✅ Advanced configuration shown")
            
            # 显示关键指标
            avg_response_time = np.mean([r['response_time'] for r in scenario_results.values()])
            trigger_rate = sum(1 for r in scenario_results.values() if r['replanning_triggered']) / len(scenario_results)
            
            logger.info(f"\nKey Metrics:")
            logger.info(f"  Average response time: {avg_response_time:.3f}s")
            logger.info(f"  Trigger rate: {trigger_rate:.2%}")
            logger.info(f"  Total replanning events: {performance_summary.get('total_replanning_events', 0)}")
            
            logger.info("\n🎉 Demo completed successfully!")
            
        except Exception as e:
            logger.error(f"❌ Demo failed: {e}")
            raise


def main():
    """主函数"""
    # 创建并运行演示
    demo = ReplanningDemo()
    demo.run_complete_demo()


if __name__ == "__main__":
    main()

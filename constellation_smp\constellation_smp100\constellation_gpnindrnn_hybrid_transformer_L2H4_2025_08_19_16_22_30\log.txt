constellation_smp: 100
model: gpn
rnn: indrnn
hidden_size: 256
batch_size: 64
seed: 12346
train-size: 100000
valid-size: 10000
epochs: 3
lr: 0.0002
memory_total: 0.3
power_total: 5
dropout: 0.1
actor_lr: 0.0001
critic_lr: 0.0002
num_satellites: 3
constellation_mode: hybrid
use_transformer: True
transformer_layers: 2
transformer_heads: 4
transformer_d_model: 256
transformer_d_ff: 512
transformer_dropout: 0.1
transformer_activation: gelu
transformer_integration_mode: parallel
verbose: True
2025_08_19_16_22_30
启用Transformer增强模块，配置: {'d_model': 256, 'num_heads': 4, 'd_ff': 512, 'num_layers': 2, 'max_len': 5000, 'dropout': 0.1, 'activation': 'gelu'}

开始训练 Epoch 1/3
Epoch 1, Batch 10/1563, loss: 711.791, reward: 16.251, critic_reward: 16.730, revenue_rate: 0.4152, distance: 6.4208, memory: -0.0022, power: 0.1935, lr: 0.000100, took: 169.866s
Epoch 1, Batch 20/1563, loss: 137.565, reward: 17.940, critic_reward: 14.054, revenue_rate: 0.4599, distance: 7.1747, memory: 0.0143, power: 0.2159, lr: 0.000100, took: 183.929s
Epoch 1, Batch 30/1563, loss: 35.649, reward: 19.676, critic_reward: 20.591, revenue_rate: 0.5050, distance: 7.9486, memory: 0.0501, power: 0.2401, lr: 0.000100, took: 213.217s
Epoch 1, Batch 40/1563, loss: 38.374, reward: 18.050, critic_reward: 16.085, revenue_rate: 0.4642, distance: 7.3373, memory: 0.0654, power: 0.2239, lr: 0.000100, took: 197.436s
Epoch 1, Batch 50/1563, loss: 6.024, reward: 16.924, critic_reward: 16.697, revenue_rate: 0.4384, distance: 7.0829, memory: 0.0908, power: 0.2145, lr: 0.000100, took: 189.517s
Epoch 1, Batch 60/1563, loss: 18.962, reward: 16.916, critic_reward: 15.076, revenue_rate: 0.4385, distance: 7.0513, memory: 0.0877, power: 0.2162, lr: 0.000100, took: 183.736s
Epoch 1, Batch 70/1563, loss: 8.276, reward: 17.204, critic_reward: 17.103, revenue_rate: 0.4447, distance: 7.2497, memory: 0.1057, power: 0.2193, lr: 0.000100, took: 197.006s
Epoch 1, Batch 80/1563, loss: 13.652, reward: 16.635, critic_reward: 15.105, revenue_rate: 0.4323, distance: 7.0346, memory: 0.0942, power: 0.2131, lr: 0.000100, took: 193.986s
Epoch 1, Batch 90/1563, loss: 10.145, reward: 16.543, critic_reward: 16.849, revenue_rate: 0.4263, distance: 6.9059, memory: 0.0832, power: 0.2103, lr: 0.000100, took: 183.529s
Epoch 1, Batch 100/1563, loss: 14.384, reward: 17.484, critic_reward: 17.496, revenue_rate: 0.4546, distance: 7.4140, memory: 0.0896, power: 0.2248, lr: 0.000100, took: 198.333s
Epoch 1, Batch 110/1563, loss: 12.335, reward: 16.723, critic_reward: 18.218, revenue_rate: 0.4335, distance: 7.0604, memory: 0.0652, power: 0.2117, lr: 0.000100, took: 189.867s
Epoch 1, Batch 120/1563, loss: 24.303, reward: 16.311, critic_reward: 17.542, revenue_rate: 0.4205, distance: 6.7633, memory: 0.0583, power: 0.2027, lr: 0.000100, took: 181.369s
Epoch 1, Batch 130/1563, loss: 25.877, reward: 14.974, critic_reward: 14.975, revenue_rate: 0.3881, distance: 6.2728, memory: 0.0445, power: 0.1906, lr: 0.000100, took: 162.501s
Epoch 1, Batch 140/1563, loss: 20.068, reward: 13.938, critic_reward: 13.550, revenue_rate: 0.3588, distance: 5.7809, memory: 0.0426, power: 0.1739, lr: 0.000100, took: 149.352s
Epoch 1, Batch 150/1563, loss: 14.271, reward: 12.848, critic_reward: 12.865, revenue_rate: 0.3331, distance: 5.3500, memory: 0.0436, power: 0.1623, lr: 0.000100, took: 139.761s
Epoch 1, Batch 160/1563, loss: 16.734, reward: 12.029, critic_reward: 11.904, revenue_rate: 0.3108, distance: 4.9962, memory: 0.0449, power: 0.1520, lr: 0.000100, took: 126.949s
Epoch 1, Batch 170/1563, loss: 8.431, reward: 11.452, critic_reward: 11.821, revenue_rate: 0.2966, distance: 4.8158, memory: 0.0453, power: 0.1458, lr: 0.000100, took: 121.705s
Epoch 1, Batch 180/1563, loss: 6.476, reward: 11.878, critic_reward: 11.639, revenue_rate: 0.3070, distance: 4.9190, memory: 0.0326, power: 0.1485, lr: 0.000100, took: 127.230s
Epoch 1, Batch 190/1563, loss: 9.054, reward: 13.648, critic_reward: 13.968, revenue_rate: 0.3517, distance: 5.6620, memory: 0.0401, power: 0.1709, lr: 0.000100, took: 145.312s
Epoch 1, Batch 200/1563, loss: 9.625, reward: 15.234, critic_reward: 16.398, revenue_rate: 0.3921, distance: 6.1737, memory: 0.0180, power: 0.1879, lr: 0.000100, took: 166.070s
Epoch 1, Batch 210/1563, loss: 26.891, reward: 15.256, critic_reward: 16.038, revenue_rate: 0.3920, distance: 6.2263, memory: 0.0366, power: 0.1902, lr: 0.000100, took: 161.242s
Epoch 1, Batch 220/1563, loss: 28.510, reward: 13.027, critic_reward: 11.724, revenue_rate: 0.3351, distance: 5.2752, memory: 0.0398, power: 0.1629, lr: 0.000100, took: 142.992s
Epoch 1, Batch 230/1563, loss: 7.487, reward: 13.098, critic_reward: 13.383, revenue_rate: 0.3383, distance: 5.4883, memory: 0.0360, power: 0.1652, lr: 0.000100, took: 141.408s
Epoch 1, Batch 240/1563, loss: 12.270, reward: 14.120, critic_reward: 13.874, revenue_rate: 0.3616, distance: 5.6582, memory: 0.0100, power: 0.1733, lr: 0.000100, took: 151.497s
Epoch 1, Batch 250/1563, loss: 9.627, reward: 14.897, critic_reward: 16.213, revenue_rate: 0.3837, distance: 6.1205, memory: 0.0132, power: 0.1855, lr: 0.000100, took: 154.919s
Epoch 1, Batch 260/1563, loss: 16.972, reward: 14.594, critic_reward: 13.339, revenue_rate: 0.3748, distance: 5.9355, memory: 0.0119, power: 0.1803, lr: 0.000100, took: 152.219s
Epoch 1, Batch 270/1563, loss: 16.587, reward: 14.139, critic_reward: 14.494, revenue_rate: 0.3618, distance: 5.6611, memory: 0.0010, power: 0.1711, lr: 0.000100, took: 153.732s
Epoch 1, Batch 280/1563, loss: 18.872, reward: 14.327, critic_reward: 15.098, revenue_rate: 0.3703, distance: 5.8696, memory: 0.0028, power: 0.1776, lr: 0.000100, took: 147.646s
Epoch 1, Batch 290/1563, loss: 9.027, reward: 14.842, critic_reward: 15.404, revenue_rate: 0.3791, distance: 5.9927, memory: 0.0174, power: 0.1811, lr: 0.000100, took: 150.843s
Epoch 1, Batch 300/1563, loss: 6.188, reward: 17.172, critic_reward: 17.817, revenue_rate: 0.4376, distance: 6.8289, memory: -0.0209, power: 0.2082, lr: 0.000100, took: 183.791s
Epoch 1, Batch 310/1563, loss: 8.271, reward: 19.231, critic_reward: 18.434, revenue_rate: 0.4925, distance: 7.7895, memory: -0.0095, power: 0.2345, lr: 0.000100, took: 205.619s
Epoch 1, Batch 320/1563, loss: 5.742, reward: 20.177, critic_reward: 20.043, revenue_rate: 0.5194, distance: 8.2750, memory: -0.0002, power: 0.2491, lr: 0.000100, took: 222.360s
Epoch 1, Batch 330/1563, loss: 10.442, reward: 20.782, critic_reward: 20.883, revenue_rate: 0.5342, distance: 8.5549, memory: 0.0281, power: 0.2591, lr: 0.000100, took: 231.542s
Epoch 1, Batch 340/1563, loss: 6.602, reward: 20.037, critic_reward: 20.157, revenue_rate: 0.5164, distance: 8.1353, memory: -0.0201, power: 0.2451, lr: 0.000100, took: 216.091s
Epoch 1, Batch 350/1563, loss: 6.046, reward: 18.726, critic_reward: 18.000, revenue_rate: 0.4822, distance: 7.6436, memory: -0.0079, power: 0.2310, lr: 0.000100, took: 205.722s
Epoch 1, Batch 360/1563, loss: 5.582, reward: 18.786, critic_reward: 18.864, revenue_rate: 0.4841, distance: 7.6754, memory: 0.0038, power: 0.2325, lr: 0.000100, took: 205.555s
Epoch 1, Batch 370/1563, loss: 7.411, reward: 17.197, critic_reward: 17.596, revenue_rate: 0.4429, distance: 7.0119, memory: 0.0003, power: 0.2130, lr: 0.000100, took: 192.431s
Epoch 1, Batch 380/1563, loss: 7.537, reward: 16.998, critic_reward: 16.935, revenue_rate: 0.4386, distance: 7.0188, memory: 0.0085, power: 0.2128, lr: 0.000100, took: 187.392s
Epoch 1, Batch 390/1563, loss: 10.781, reward: 16.152, critic_reward: 16.735, revenue_rate: 0.4167, distance: 6.6166, memory: 0.0134, power: 0.2009, lr: 0.000100, took: 176.279s
Epoch 1, Batch 400/1563, loss: 6.044, reward: 14.796, critic_reward: 14.538, revenue_rate: 0.3830, distance: 6.2066, memory: 0.0349, power: 0.1862, lr: 0.000100, took: 159.588s
Epoch 1, Batch 410/1563, loss: 7.279, reward: 14.781, critic_reward: 15.609, revenue_rate: 0.3801, distance: 6.0345, memory: 0.0243, power: 0.1854, lr: 0.000100, took: 162.052s
Epoch 1, Batch 420/1563, loss: 7.779, reward: 14.260, critic_reward: 13.877, revenue_rate: 0.3671, distance: 5.9172, memory: 0.0295, power: 0.1789, lr: 0.000100, took: 150.169s
Epoch 1, Batch 430/1563, loss: 5.890, reward: 12.458, critic_reward: 13.078, revenue_rate: 0.3221, distance: 5.1302, memory: 0.0291, power: 0.1572, lr: 0.000100, took: 136.836s
Epoch 1, Batch 440/1563, loss: 4.801, reward: 12.207, critic_reward: 12.359, revenue_rate: 0.3140, distance: 5.0094, memory: 0.0300, power: 0.1524, lr: 0.000100, took: 129.679s
Epoch 1, Batch 450/1563, loss: 4.982, reward: 12.083, critic_reward: 12.722, revenue_rate: 0.3115, distance: 4.9755, memory: 0.0330, power: 0.1532, lr: 0.000100, took: 130.623s
Epoch 1, Batch 460/1563, loss: 7.043, reward: 12.151, critic_reward: 11.501, revenue_rate: 0.3143, distance: 5.0873, memory: 0.0431, power: 0.1531, lr: 0.000100, took: 127.867s
Epoch 1, Batch 470/1563, loss: 4.515, reward: 12.123, critic_reward: 12.365, revenue_rate: 0.3120, distance: 5.0166, memory: 0.0383, power: 0.1521, lr: 0.000100, took: 129.866s
Epoch 1, Batch 480/1563, loss: 6.591, reward: 11.900, critic_reward: 11.949, revenue_rate: 0.3075, distance: 4.9642, memory: 0.0398, power: 0.1504, lr: 0.000100, took: 129.628s
Epoch 1, Batch 490/1563, loss: 7.632, reward: 12.147, critic_reward: 11.891, revenue_rate: 0.3130, distance: 5.0591, memory: 0.0376, power: 0.1531, lr: 0.000100, took: 127.784s
Epoch 1, Batch 500/1563, loss: 3.909, reward: 11.933, critic_reward: 11.962, revenue_rate: 0.3074, distance: 4.9223, memory: 0.0425, power: 0.1504, lr: 0.000100, took: 129.048s
Epoch 1, Batch 510/1563, loss: 5.814, reward: 11.679, critic_reward: 11.164, revenue_rate: 0.3018, distance: 4.9317, memory: 0.0368, power: 0.1470, lr: 0.000100, took: 127.737s
Epoch 1, Batch 520/1563, loss: 5.230, reward: 11.673, critic_reward: 11.519, revenue_rate: 0.3017, distance: 4.8346, memory: 0.0294, power: 0.1455, lr: 0.000100, took: 125.317s
Epoch 1, Batch 530/1563, loss: 5.966, reward: 11.320, critic_reward: 11.559, revenue_rate: 0.2916, distance: 4.7320, memory: 0.0429, power: 0.1430, lr: 0.000100, took: 123.546s
Epoch 1, Batch 540/1563, loss: 5.716, reward: 11.318, critic_reward: 10.892, revenue_rate: 0.2910, distance: 4.6483, memory: 0.0289, power: 0.1409, lr: 0.000100, took: 120.790s
Epoch 1, Batch 550/1563, loss: 6.501, reward: 11.519, critic_reward: 11.982, revenue_rate: 0.2977, distance: 4.8014, memory: 0.0275, power: 0.1455, lr: 0.000100, took: 125.141s
Epoch 1, Batch 560/1563, loss: 5.423, reward: 11.056, critic_reward: 11.076, revenue_rate: 0.2851, distance: 4.5752, memory: 0.0300, power: 0.1405, lr: 0.000100, took: 121.740s
Epoch 1, Batch 570/1563, loss: 8.302, reward: 10.988, critic_reward: 10.485, revenue_rate: 0.2843, distance: 4.6069, memory: 0.0375, power: 0.1406, lr: 0.000100, took: 120.783s
Epoch 1, Batch 580/1563, loss: 3.472, reward: 11.256, critic_reward: 11.275, revenue_rate: 0.2898, distance: 4.6270, memory: 0.0245, power: 0.1413, lr: 0.000100, took: 122.551s
Epoch 1, Batch 590/1563, loss: 4.410, reward: 11.685, critic_reward: 12.045, revenue_rate: 0.3015, distance: 4.7778, memory: 0.0334, power: 0.1464, lr: 0.000100, took: 123.344s
Epoch 1, Batch 600/1563, loss: 5.014, reward: 11.778, critic_reward: 11.668, revenue_rate: 0.3053, distance: 4.8721, memory: 0.0301, power: 0.1489, lr: 0.000100, took: 122.224s
Epoch 1, Batch 610/1563, loss: 4.785, reward: 11.286, critic_reward: 11.843, revenue_rate: 0.2916, distance: 4.6746, memory: 0.0286, power: 0.1411, lr: 0.000100, took: 119.167s
Epoch 1, Batch 620/1563, loss: 4.869, reward: 11.143, critic_reward: 11.769, revenue_rate: 0.2870, distance: 4.5595, memory: 0.0267, power: 0.1385, lr: 0.000100, took: 117.147s
Epoch 1, Batch 630/1563, loss: 4.605, reward: 10.587, critic_reward: 10.290, revenue_rate: 0.2743, distance: 4.4834, memory: 0.0337, power: 0.1343, lr: 0.000100, took: 119.203s
Epoch 1, Batch 640/1563, loss: 6.092, reward: 11.276, critic_reward: 11.965, revenue_rate: 0.2909, distance: 4.6578, memory: 0.0334, power: 0.1426, lr: 0.000100, took: 121.432s
Epoch 1, Batch 650/1563, loss: 9.804, reward: 10.882, critic_reward: 9.851, revenue_rate: 0.2823, distance: 4.5604, memory: 0.0386, power: 0.1383, lr: 0.000100, took: 118.410s
Epoch 1, Batch 660/1563, loss: 4.459, reward: 10.809, critic_reward: 11.411, revenue_rate: 0.2792, distance: 4.5015, memory: 0.0263, power: 0.1365, lr: 0.000100, took: 121.161s
Epoch 1, Batch 670/1563, loss: 3.752, reward: 10.665, critic_reward: 10.593, revenue_rate: 0.2755, distance: 4.4334, memory: 0.0454, power: 0.1349, lr: 0.000100, took: 119.608s
Epoch 1, Batch 680/1563, loss: 4.559, reward: 11.083, critic_reward: 10.182, revenue_rate: 0.2858, distance: 4.6108, memory: 0.0374, power: 0.1389, lr: 0.000100, took: 120.980s
Epoch 1, Batch 690/1563, loss: 13.273, reward: 11.193, critic_reward: 13.168, revenue_rate: 0.2884, distance: 4.5910, memory: 0.0308, power: 0.1394, lr: 0.000100, took: 124.517s
Epoch 1, Batch 700/1563, loss: 7.472, reward: 11.789, critic_reward: 11.363, revenue_rate: 0.3036, distance: 4.8309, memory: 0.0383, power: 0.1471, lr: 0.000100, took: 128.747s
Epoch 1, Batch 710/1563, loss: 4.245, reward: 11.499, critic_reward: 11.808, revenue_rate: 0.2972, distance: 4.7809, memory: 0.0460, power: 0.1468, lr: 0.000100, took: 123.161s
Epoch 1, Batch 720/1563, loss: 3.931, reward: 11.029, critic_reward: 11.458, revenue_rate: 0.2850, distance: 4.5469, memory: 0.0236, power: 0.1389, lr: 0.000100, took: 121.213s
Epoch 1, Batch 730/1563, loss: 2.942, reward: 10.760, critic_reward: 10.598, revenue_rate: 0.2787, distance: 4.5140, memory: 0.0353, power: 0.1365, lr: 0.000100, took: 117.586s
Epoch 1, Batch 740/1563, loss: 3.506, reward: 10.556, critic_reward: 10.892, revenue_rate: 0.2732, distance: 4.3748, memory: 0.0291, power: 0.1329, lr: 0.000100, took: 114.882s
Epoch 1, Batch 750/1563, loss: 3.257, reward: 10.337, critic_reward: 10.255, revenue_rate: 0.2670, distance: 4.1857, memory: 0.0207, power: 0.1285, lr: 0.000100, took: 109.958s
Epoch 1, Batch 760/1563, loss: 4.160, reward: 10.452, critic_reward: 9.841, revenue_rate: 0.2704, distance: 4.3196, memory: 0.0215, power: 0.1312, lr: 0.000100, took: 111.426s
Epoch 1, Batch 770/1563, loss: 3.358, reward: 10.300, critic_reward: 10.533, revenue_rate: 0.2676, distance: 4.3603, memory: 0.0411, power: 0.1311, lr: 0.000100, took: 113.737s
Epoch 1, Batch 780/1563, loss: 3.569, reward: 10.307, critic_reward: 10.570, revenue_rate: 0.2648, distance: 4.2489, memory: 0.0248, power: 0.1284, lr: 0.000100, took: 111.363s
Epoch 1, Batch 790/1563, loss: 4.767, reward: 10.588, critic_reward: 10.629, revenue_rate: 0.2735, distance: 4.3789, memory: 0.0344, power: 0.1331, lr: 0.000100, took: 113.946s
Epoch 1, Batch 800/1563, loss: 7.330, reward: 10.527, critic_reward: 11.141, revenue_rate: 0.2724, distance: 4.3685, memory: 0.0332, power: 0.1327, lr: 0.000100, took: 112.611s
Epoch 1, Batch 810/1563, loss: 4.992, reward: 10.487, critic_reward: 10.166, revenue_rate: 0.2709, distance: 4.3400, memory: 0.0353, power: 0.1315, lr: 0.000100, took: 111.556s
Epoch 1, Batch 820/1563, loss: 3.443, reward: 10.323, critic_reward: 10.690, revenue_rate: 0.2664, distance: 4.2528, memory: 0.0438, power: 0.1296, lr: 0.000100, took: 113.142s
Epoch 1, Batch 830/1563, loss: 4.799, reward: 10.741, critic_reward: 10.543, revenue_rate: 0.2768, distance: 4.4807, memory: 0.0388, power: 0.1367, lr: 0.000100, took: 117.808s
Epoch 1, Batch 840/1563, loss: 3.725, reward: 10.591, critic_reward: 10.714, revenue_rate: 0.2749, distance: 4.4383, memory: 0.0311, power: 0.1348, lr: 0.000100, took: 118.366s
Epoch 1, Batch 850/1563, loss: 3.433, reward: 10.896, critic_reward: 10.811, revenue_rate: 0.2810, distance: 4.4849, memory: 0.0321, power: 0.1367, lr: 0.000100, took: 116.912s
Epoch 1, Batch 860/1563, loss: 3.277, reward: 10.731, critic_reward: 11.152, revenue_rate: 0.2774, distance: 4.4524, memory: 0.0412, power: 0.1369, lr: 0.000100, took: 120.592s
Epoch 1, Batch 870/1563, loss: 5.037, reward: 10.892, critic_reward: 10.750, revenue_rate: 0.2812, distance: 4.5067, memory: 0.0388, power: 0.1377, lr: 0.000100, took: 121.358s
Epoch 1, Batch 880/1563, loss: 3.934, reward: 10.974, critic_reward: 11.369, revenue_rate: 0.2827, distance: 4.5344, memory: 0.0370, power: 0.1378, lr: 0.000100, took: 121.939s
Epoch 1, Batch 890/1563, loss: 3.326, reward: 10.899, critic_reward: 11.022, revenue_rate: 0.2811, distance: 4.5353, memory: 0.0358, power: 0.1376, lr: 0.000100, took: 120.191s
Epoch 1, Batch 900/1563, loss: 3.830, reward: 11.074, critic_reward: 10.458, revenue_rate: 0.2865, distance: 4.6063, memory: 0.0525, power: 0.1420, lr: 0.000100, took: 118.709s
Epoch 1, Batch 910/1563, loss: 3.616, reward: 10.905, critic_reward: 10.670, revenue_rate: 0.2822, distance: 4.5698, memory: 0.0423, power: 0.1403, lr: 0.000100, took: 122.804s
Epoch 1, Batch 920/1563, loss: 5.039, reward: 11.193, critic_reward: 11.782, revenue_rate: 0.2895, distance: 4.6727, memory: 0.0300, power: 0.1416, lr: 0.000100, took: 120.727s
Epoch 1, Batch 930/1563, loss: 5.237, reward: 11.306, critic_reward: 11.822, revenue_rate: 0.2905, distance: 4.6911, memory: 0.0352, power: 0.1426, lr: 0.000100, took: 125.022s
Epoch 1, Batch 940/1563, loss: 10.282, reward: 11.026, critic_reward: 8.799, revenue_rate: 0.2841, distance: 4.5773, memory: 0.0305, power: 0.1383, lr: 0.000100, took: 120.782s
Epoch 1, Batch 950/1563, loss: 6.640, reward: 10.871, critic_reward: 12.076, revenue_rate: 0.2812, distance: 4.5408, memory: 0.0318, power: 0.1383, lr: 0.000100, took: 120.549s
Epoch 1, Batch 960/1563, loss: 5.218, reward: 11.526, critic_reward: 11.324, revenue_rate: 0.2990, distance: 4.9003, memory: 0.0531, power: 0.1462, lr: 0.000100, took: 126.154s
Epoch 1, Batch 970/1563, loss: 3.469, reward: 10.908, critic_reward: 10.771, revenue_rate: 0.2814, distance: 4.5101, memory: 0.0374, power: 0.1373, lr: 0.000100, took: 122.184s
Epoch 1, Batch 980/1563, loss: 4.448, reward: 11.490, critic_reward: 11.232, revenue_rate: 0.2974, distance: 4.8222, memory: 0.0486, power: 0.1463, lr: 0.000100, took: 124.704s
Epoch 1, Batch 990/1563, loss: 5.678, reward: 11.218, critic_reward: 10.787, revenue_rate: 0.2906, distance: 4.7083, memory: 0.0542, power: 0.1433, lr: 0.000100, took: 122.956s
Epoch 1, Batch 1000/1563, loss: 3.986, reward: 10.982, critic_reward: 10.932, revenue_rate: 0.2840, distance: 4.6001, memory: 0.0340, power: 0.1372, lr: 0.000100, took: 120.047s
Epoch 1, Batch 1010/1563, loss: 4.998, reward: 11.311, critic_reward: 11.842, revenue_rate: 0.2927, distance: 4.7433, memory: 0.0388, power: 0.1434, lr: 0.000100, took: 126.501s
Epoch 1, Batch 1020/1563, loss: 3.239, reward: 11.070, critic_reward: 11.077, revenue_rate: 0.2860, distance: 4.6248, memory: 0.0452, power: 0.1391, lr: 0.000100, took: 125.361s
Epoch 1, Batch 1030/1563, loss: 4.732, reward: 10.975, critic_reward: 10.516, revenue_rate: 0.2846, distance: 4.6322, memory: 0.0397, power: 0.1398, lr: 0.000100, took: 122.163s
Epoch 1, Batch 1040/1563, loss: 6.319, reward: 11.219, critic_reward: 10.432, revenue_rate: 0.2911, distance: 4.7007, memory: 0.0446, power: 0.1418, lr: 0.000100, took: 125.118s
Epoch 1, Batch 1050/1563, loss: 3.321, reward: 10.962, critic_reward: 11.155, revenue_rate: 0.2828, distance: 4.5577, memory: 0.0402, power: 0.1383, lr: 0.000100, took: 122.828s
Epoch 1, Batch 1060/1563, loss: 3.509, reward: 11.336, critic_reward: 11.306, revenue_rate: 0.2929, distance: 4.7327, memory: 0.0371, power: 0.1431, lr: 0.000100, took: 120.370s
Epoch 1, Batch 1070/1563, loss: 3.716, reward: 10.676, critic_reward: 11.031, revenue_rate: 0.2778, distance: 4.5141, memory: 0.0345, power: 0.1371, lr: 0.000100, took: 119.172s
Epoch 1, Batch 1080/1563, loss: 6.814, reward: 11.131, critic_reward: 10.273, revenue_rate: 0.2877, distance: 4.6546, memory: 0.0396, power: 0.1401, lr: 0.000100, took: 121.383s
Epoch 1, Batch 1090/1563, loss: 4.691, reward: 11.610, critic_reward: 11.893, revenue_rate: 0.3003, distance: 4.8406, memory: 0.0379, power: 0.1469, lr: 0.000100, took: 126.097s
Epoch 1, Batch 1100/1563, loss: 12.990, reward: 10.923, critic_reward: 11.614, revenue_rate: 0.2840, distance: 4.6594, memory: 0.0516, power: 0.1406, lr: 0.000100, took: 120.571s
Epoch 1, Batch 1110/1563, loss: 5.808, reward: 11.564, critic_reward: 11.221, revenue_rate: 0.2987, distance: 4.8107, memory: 0.0416, power: 0.1446, lr: 0.000100, took: 124.528s
Epoch 1, Batch 1120/1563, loss: 6.699, reward: 12.177, critic_reward: 11.711, revenue_rate: 0.3136, distance: 5.0587, memory: 0.0462, power: 0.1536, lr: 0.000100, took: 127.162s
Epoch 1, Batch 1130/1563, loss: 4.966, reward: 12.033, critic_reward: 11.634, revenue_rate: 0.3115, distance: 5.0792, memory: 0.0468, power: 0.1528, lr: 0.000100, took: 130.652s
Epoch 1, Batch 1140/1563, loss: 4.430, reward: 12.146, critic_reward: 12.741, revenue_rate: 0.3141, distance: 5.1279, memory: 0.0532, power: 0.1544, lr: 0.000100, took: 127.063s
Epoch 1, Batch 1150/1563, loss: 4.795, reward: 12.092, critic_reward: 12.032, revenue_rate: 0.3121, distance: 5.0286, memory: 0.0454, power: 0.1523, lr: 0.000100, took: 129.989s
Epoch 1, Batch 1160/1563, loss: 6.226, reward: 11.947, critic_reward: 11.184, revenue_rate: 0.3092, distance: 4.9607, memory: 0.0554, power: 0.1524, lr: 0.000100, took: 127.887s
Epoch 1, Batch 1170/1563, loss: 3.958, reward: 11.833, critic_reward: 11.812, revenue_rate: 0.3052, distance: 4.9547, memory: 0.0499, power: 0.1506, lr: 0.000100, took: 128.676s
Epoch 1, Batch 1180/1563, loss: 4.388, reward: 11.369, critic_reward: 11.387, revenue_rate: 0.2941, distance: 4.7857, memory: 0.0432, power: 0.1445, lr: 0.000100, took: 125.904s
Epoch 1, Batch 1190/1563, loss: 5.364, reward: 11.510, critic_reward: 11.690, revenue_rate: 0.2967, distance: 4.8148, memory: 0.0372, power: 0.1453, lr: 0.000100, took: 130.086s
Epoch 1, Batch 1200/1563, loss: 4.067, reward: 12.072, critic_reward: 11.923, revenue_rate: 0.3122, distance: 5.0620, memory: 0.0546, power: 0.1525, lr: 0.000100, took: 125.848s
Epoch 1, Batch 1210/1563, loss: 4.465, reward: 11.685, critic_reward: 11.790, revenue_rate: 0.3032, distance: 4.9008, memory: 0.0458, power: 0.1487, lr: 0.000100, took: 129.990s
Epoch 1, Batch 1220/1563, loss: 4.216, reward: 12.073, critic_reward: 12.079, revenue_rate: 0.3129, distance: 5.1181, memory: 0.0600, power: 0.1534, lr: 0.000100, took: 133.159s
Epoch 1, Batch 1230/1563, loss: 3.505, reward: 11.867, critic_reward: 12.226, revenue_rate: 0.3061, distance: 4.9432, memory: 0.0542, power: 0.1500, lr: 0.000100, took: 129.790s
Epoch 1, Batch 1240/1563, loss: 4.473, reward: 11.429, critic_reward: 11.078, revenue_rate: 0.2977, distance: 4.8968, memory: 0.0614, power: 0.1502, lr: 0.000100, took: 127.903s
Epoch 1, Batch 1250/1563, loss: 5.383, reward: 11.801, critic_reward: 11.249, revenue_rate: 0.3066, distance: 5.0074, memory: 0.0509, power: 0.1516, lr: 0.000100, took: 132.351s
Epoch 1, Batch 1260/1563, loss: 3.664, reward: 11.851, critic_reward: 12.211, revenue_rate: 0.3067, distance: 4.9844, memory: 0.0633, power: 0.1528, lr: 0.000100, took: 131.987s
Epoch 1, Batch 1270/1563, loss: 4.857, reward: 12.636, critic_reward: 12.721, revenue_rate: 0.3274, distance: 5.3844, memory: 0.0560, power: 0.1621, lr: 0.000100, took: 137.184s
Epoch 1, Batch 1280/1563, loss: 7.365, reward: 11.891, critic_reward: 12.872, revenue_rate: 0.3071, distance: 4.9989, memory: 0.0557, power: 0.1523, lr: 0.000100, took: 130.757s
Epoch 1, Batch 1290/1563, loss: 6.604, reward: 12.658, critic_reward: 12.554, revenue_rate: 0.3283, distance: 5.3310, memory: 0.0581, power: 0.1614, lr: 0.000100, took: 141.325s
Epoch 1, Batch 1300/1563, loss: 6.266, reward: 12.634, critic_reward: 12.065, revenue_rate: 0.3270, distance: 5.3675, memory: 0.0660, power: 0.1620, lr: 0.000100, took: 136.695s
Epoch 1, Batch 1310/1563, loss: 5.195, reward: 12.331, critic_reward: 13.163, revenue_rate: 0.3210, distance: 5.2650, memory: 0.0762, power: 0.1606, lr: 0.000100, took: 142.069s
Epoch 1, Batch 1320/1563, loss: 6.506, reward: 13.015, critic_reward: 12.562, revenue_rate: 0.3390, distance: 5.5744, memory: 0.0860, power: 0.1689, lr: 0.000100, took: 143.540s
Epoch 1, Batch 1330/1563, loss: 5.393, reward: 13.016, critic_reward: 12.534, revenue_rate: 0.3392, distance: 5.6404, memory: 0.0971, power: 0.1693, lr: 0.000100, took: 142.981s
Epoch 1, Batch 1340/1563, loss: 5.525, reward: 13.561, critic_reward: 13.235, revenue_rate: 0.3538, distance: 5.9173, memory: 0.0979, power: 0.1778, lr: 0.000100, took: 155.007s
Epoch 1, Batch 1350/1563, loss: 4.835, reward: 13.936, critic_reward: 13.945, revenue_rate: 0.3616, distance: 6.0251, memory: 0.1052, power: 0.1826, lr: 0.000100, took: 158.659s
Epoch 1, Batch 1360/1563, loss: 5.870, reward: 15.101, critic_reward: 14.103, revenue_rate: 0.3938, distance: 6.5265, memory: 0.1118, power: 0.1983, lr: 0.000100, took: 163.175s
Epoch 1, Batch 1370/1563, loss: 4.890, reward: 16.160, critic_reward: 16.567, revenue_rate: 0.4208, distance: 7.0945, memory: 0.1293, power: 0.2136, lr: 0.000100, took: 186.340s
Epoch 1, Batch 1380/1563, loss: 5.802, reward: 16.956, critic_reward: 16.983, revenue_rate: 0.4432, distance: 7.4273, memory: 0.1429, power: 0.2251, lr: 0.000100, took: 202.855s
Epoch 1, Batch 1390/1563, loss: 10.734, reward: 17.044, critic_reward: 18.112, revenue_rate: 0.4472, distance: 7.4966, memory: 0.1418, power: 0.2259, lr: 0.000100, took: 206.463s
Epoch 1, Batch 1400/1563, loss: 7.085, reward: 16.342, critic_reward: 17.391, revenue_rate: 0.4290, distance: 7.2355, memory: 0.1301, power: 0.2167, lr: 0.000100, took: 196.536s
Epoch 1, Batch 1410/1563, loss: 5.989, reward: 16.464, critic_reward: 15.709, revenue_rate: 0.4292, distance: 7.0261, memory: 0.1150, power: 0.2147, lr: 0.000100, took: 189.952s
Epoch 1, Batch 1420/1563, loss: 4.817, reward: 16.659, critic_reward: 16.667, revenue_rate: 0.4330, distance: 7.1761, memory: 0.1218, power: 0.2185, lr: 0.000100, took: 195.398s
Epoch 1, Batch 1430/1563, loss: 5.333, reward: 17.493, critic_reward: 17.662, revenue_rate: 0.4562, distance: 7.5866, memory: 0.1398, power: 0.2307, lr: 0.000100, took: 206.312s
Epoch 1, Batch 1440/1563, loss: 5.303, reward: 18.453, critic_reward: 17.798, revenue_rate: 0.4824, distance: 8.1243, memory: 0.1535, power: 0.2459, lr: 0.000100, took: 221.128s
Epoch 1, Batch 1450/1563, loss: 7.388, reward: 19.697, critic_reward: 19.820, revenue_rate: 0.5162, distance: 8.6806, memory: 0.1712, power: 0.2625, lr: 0.000100, took: 238.799s
Epoch 1, Batch 1460/1563, loss: 7.358, reward: 20.844, critic_reward: 20.463, revenue_rate: 0.5481, distance: 9.3761, memory: 0.1975, power: 0.2794, lr: 0.000100, took: 246.928s
Epoch 1, Batch 1470/1563, loss: 13.063, reward: 21.649, critic_reward: 20.729, revenue_rate: 0.5651, distance: 9.5091, memory: 0.1831, power: 0.2888, lr: 0.000100, took: 255.916s
Epoch 1, Batch 1480/1563, loss: 10.333, reward: 21.002, critic_reward: 21.360, revenue_rate: 0.5498, distance: 9.2107, memory: 0.1814, power: 0.2795, lr: 0.000100, took: 252.913s
Epoch 1, Batch 1490/1563, loss: 7.264, reward: 19.453, critic_reward: 19.840, revenue_rate: 0.5086, distance: 8.4974, memory: 0.1525, power: 0.2533, lr: 0.000100, took: 228.693s
Epoch 1, Batch 1500/1563, loss: 8.793, reward: 18.076, critic_reward: 17.188, revenue_rate: 0.4719, distance: 7.8699, memory: 0.1396, power: 0.2383, lr: 0.000100, took: 216.328s
Epoch 1, Batch 1510/1563, loss: 6.010, reward: 17.431, critic_reward: 17.665, revenue_rate: 0.4553, distance: 7.6506, memory: 0.1373, power: 0.2293, lr: 0.000100, took: 202.259s
Epoch 1, Batch 1520/1563, loss: 6.659, reward: 16.893, critic_reward: 16.037, revenue_rate: 0.4389, distance: 7.2217, memory: 0.1222, power: 0.2198, lr: 0.000100, took: 194.568s
Epoch 1, Batch 1530/1563, loss: 4.058, reward: 15.799, critic_reward: 15.659, revenue_rate: 0.4119, distance: 6.8055, memory: 0.1034, power: 0.2022, lr: 0.000100, took: 182.149s
Epoch 1, Batch 1540/1563, loss: 5.548, reward: 16.360, critic_reward: 16.163, revenue_rate: 0.4286, distance: 7.1780, memory: 0.1266, power: 0.2175, lr: 0.000100, took: 189.995s
Epoch 1, Batch 1550/1563, loss: 4.374, reward: 18.313, critic_reward: 18.250, revenue_rate: 0.4777, distance: 7.9667, memory: 0.1384, power: 0.2398, lr: 0.000100, took: 206.848s
Epoch 1, Batch 1560/1563, loss: 6.163, reward: 18.045, critic_reward: 18.910, revenue_rate: 0.4717, distance: 7.9029, memory: 0.1412, power: 0.2375, lr: 0.000100, took: 212.408s
开始验证...
Test Batch 0/157, reward: 17.916, revenue_rate: 0.4708, distance: 7.8992, memory: 0.1913, power: 0.2468
Test Batch 1/157, reward: 16.686, revenue_rate: 0.4378, distance: 7.4260, memory: 0.1451, power: 0.2256
Test Batch 2/157, reward: 17.354, revenue_rate: 0.4541, distance: 7.4492, memory: 0.1536, power: 0.2293
Test Batch 3/157, reward: 18.080, revenue_rate: 0.4752, distance: 7.7387, memory: 0.1427, power: 0.2407
Test Batch 4/157, reward: 17.810, revenue_rate: 0.4649, distance: 7.8174, memory: 0.1350, power: 0.2344
Test Batch 5/157, reward: 16.546, revenue_rate: 0.4275, distance: 7.1193, memory: 0.1287, power: 0.2199
Test Batch 6/157, reward: 17.287, revenue_rate: 0.4503, distance: 7.3568, memory: 0.1139, power: 0.2274
Test Batch 7/157, reward: 17.966, revenue_rate: 0.4706, distance: 7.8134, memory: 0.1335, power: 0.2395
Test Batch 8/157, reward: 17.537, revenue_rate: 0.4589, distance: 7.4965, memory: 0.1391, power: 0.2288
Test Batch 9/157, reward: 16.406, revenue_rate: 0.4252, distance: 7.0946, memory: 0.1255, power: 0.2154
Test Batch 10/157, reward: 17.328, revenue_rate: 0.4574, distance: 7.5400, memory: 0.1604, power: 0.2305
Test Batch 11/157, reward: 17.741, revenue_rate: 0.4589, distance: 7.7559, memory: 0.1100, power: 0.2243
Test Batch 12/157, reward: 18.067, revenue_rate: 0.4711, distance: 7.9310, memory: 0.1440, power: 0.2382
Test Batch 13/157, reward: 15.963, revenue_rate: 0.4202, distance: 7.1301, memory: 0.1500, power: 0.2093
Test Batch 14/157, reward: 17.068, revenue_rate: 0.4455, distance: 7.3006, memory: 0.1487, power: 0.2205
Test Batch 15/157, reward: 16.804, revenue_rate: 0.4344, distance: 7.0340, memory: 0.1292, power: 0.2162
Test Batch 16/157, reward: 17.686, revenue_rate: 0.4591, distance: 7.7902, memory: 0.1535, power: 0.2316
Test Batch 17/157, reward: 17.567, revenue_rate: 0.4570, distance: 7.5628, memory: 0.1245, power: 0.2287
Test Batch 18/157, reward: 17.960, revenue_rate: 0.4656, distance: 7.7008, memory: 0.1258, power: 0.2400
Test Batch 19/157, reward: 16.436, revenue_rate: 0.4289, distance: 7.0523, memory: 0.1291, power: 0.2156
Test Batch 20/157, reward: 16.780, revenue_rate: 0.4423, distance: 7.3339, memory: 0.1497, power: 0.2224
Test Batch 21/157, reward: 17.725, revenue_rate: 0.4641, distance: 8.0469, memory: 0.1280, power: 0.2307
Test Batch 22/157, reward: 17.042, revenue_rate: 0.4522, distance: 7.7520, memory: 0.1377, power: 0.2223
Test Batch 23/157, reward: 17.261, revenue_rate: 0.4529, distance: 7.3912, memory: 0.1378, power: 0.2271
Test Batch 24/157, reward: 17.207, revenue_rate: 0.4488, distance: 7.2074, memory: 0.1399, power: 0.2235
Test Batch 25/157, reward: 18.256, revenue_rate: 0.4708, distance: 7.7396, memory: 0.1578, power: 0.2376
Test Batch 26/157, reward: 17.947, revenue_rate: 0.4679, distance: 7.8388, memory: 0.1424, power: 0.2382
Test Batch 27/157, reward: 18.286, revenue_rate: 0.4763, distance: 7.7433, memory: 0.1311, power: 0.2417
Test Batch 28/157, reward: 18.218, revenue_rate: 0.4762, distance: 8.0916, memory: 0.1191, power: 0.2366
Test Batch 29/157, reward: 15.915, revenue_rate: 0.4172, distance: 7.1858, memory: 0.1467, power: 0.2135
Test Batch 30/157, reward: 17.440, revenue_rate: 0.4603, distance: 7.6716, memory: 0.1720, power: 0.2359
Test Batch 31/157, reward: 17.082, revenue_rate: 0.4438, distance: 7.3234, memory: 0.1489, power: 0.2205
Test Batch 32/157, reward: 17.626, revenue_rate: 0.4580, distance: 7.5610, memory: 0.1425, power: 0.2299
Test Batch 33/157, reward: 17.605, revenue_rate: 0.4570, distance: 7.5729, memory: 0.1409, power: 0.2262
Test Batch 34/157, reward: 17.653, revenue_rate: 0.4575, distance: 7.5631, memory: 0.1272, power: 0.2277
Test Batch 35/157, reward: 18.027, revenue_rate: 0.4714, distance: 8.1419, memory: 0.1500, power: 0.2385
Test Batch 36/157, reward: 19.097, revenue_rate: 0.4943, distance: 8.1014, memory: 0.1426, power: 0.2520
Test Batch 37/157, reward: 18.529, revenue_rate: 0.4742, distance: 7.7601, memory: 0.1284, power: 0.2353
Test Batch 38/157, reward: 16.919, revenue_rate: 0.4405, distance: 7.1226, memory: 0.0854, power: 0.2168
Test Batch 39/157, reward: 16.746, revenue_rate: 0.4384, distance: 7.3237, memory: 0.1542, power: 0.2184
Test Batch 40/157, reward: 16.325, revenue_rate: 0.4231, distance: 7.0865, memory: 0.0947, power: 0.2125
Test Batch 41/157, reward: 17.498, revenue_rate: 0.4602, distance: 7.7348, memory: 0.1325, power: 0.2358
Test Batch 42/157, reward: 20.667, revenue_rate: 0.5350, distance: 8.8498, memory: 0.1697, power: 0.2771
Test Batch 43/157, reward: 18.653, revenue_rate: 0.4848, distance: 8.3202, memory: 0.1484, power: 0.2497
Test Batch 44/157, reward: 17.480, revenue_rate: 0.4596, distance: 7.5114, memory: 0.1400, power: 0.2285
Test Batch 45/157, reward: 16.462, revenue_rate: 0.4299, distance: 7.0731, memory: 0.1544, power: 0.2200
Test Batch 46/157, reward: 16.248, revenue_rate: 0.4250, distance: 7.0467, memory: 0.1225, power: 0.2120
Test Batch 47/157, reward: 17.521, revenue_rate: 0.4581, distance: 7.5044, memory: 0.1607, power: 0.2340
Test Batch 48/157, reward: 16.782, revenue_rate: 0.4407, distance: 7.5935, memory: 0.1615, power: 0.2286
Test Batch 49/157, reward: 17.421, revenue_rate: 0.4525, distance: 7.5347, memory: 0.1372, power: 0.2271
Test Batch 50/157, reward: 16.597, revenue_rate: 0.4240, distance: 7.0422, memory: 0.1289, power: 0.2137
Test Batch 51/157, reward: 18.095, revenue_rate: 0.4723, distance: 7.6949, memory: 0.1661, power: 0.2358
Test Batch 52/157, reward: 17.211, revenue_rate: 0.4491, distance: 7.4043, memory: 0.1085, power: 0.2251
Test Batch 53/157, reward: 16.038, revenue_rate: 0.4201, distance: 7.1089, memory: 0.1463, power: 0.2124
Test Batch 54/157, reward: 18.069, revenue_rate: 0.4690, distance: 7.6118, memory: 0.1318, power: 0.2307
Test Batch 55/157, reward: 17.017, revenue_rate: 0.4465, distance: 7.5176, memory: 0.1134, power: 0.2254
Test Batch 56/157, reward: 19.726, revenue_rate: 0.5046, distance: 8.3205, memory: 0.1497, power: 0.2504
Test Batch 57/157, reward: 17.294, revenue_rate: 0.4517, distance: 7.3201, memory: 0.1341, power: 0.2304
Test Batch 58/157, reward: 16.903, revenue_rate: 0.4497, distance: 7.5156, memory: 0.1660, power: 0.2348
Test Batch 59/157, reward: 18.210, revenue_rate: 0.4731, distance: 7.7753, memory: 0.1529, power: 0.2397
Test Batch 60/157, reward: 18.546, revenue_rate: 0.4890, distance: 8.1768, memory: 0.1650, power: 0.2484
Test Batch 61/157, reward: 18.140, revenue_rate: 0.4713, distance: 7.8943, memory: 0.1624, power: 0.2420
Test Batch 62/157, reward: 18.283, revenue_rate: 0.4747, distance: 8.0490, memory: 0.1703, power: 0.2437
Test Batch 63/157, reward: 17.112, revenue_rate: 0.4479, distance: 7.4902, memory: 0.1154, power: 0.2240
Test Batch 64/157, reward: 16.544, revenue_rate: 0.4325, distance: 7.2924, memory: 0.1445, power: 0.2148
Test Batch 65/157, reward: 16.909, revenue_rate: 0.4442, distance: 7.4959, memory: 0.1258, power: 0.2254
Test Batch 66/157, reward: 16.697, revenue_rate: 0.4369, distance: 7.2988, memory: 0.1511, power: 0.2224
Test Batch 67/157, reward: 16.259, revenue_rate: 0.4259, distance: 7.2609, memory: 0.1698, power: 0.2166
Test Batch 68/157, reward: 15.778, revenue_rate: 0.4174, distance: 7.0286, memory: 0.1381, power: 0.2085
Test Batch 69/157, reward: 17.655, revenue_rate: 0.4644, distance: 7.7857, memory: 0.1217, power: 0.2319
Test Batch 70/157, reward: 18.084, revenue_rate: 0.4715, distance: 7.7276, memory: 0.1403, power: 0.2322
Test Batch 71/157, reward: 19.011, revenue_rate: 0.5006, distance: 8.3786, memory: 0.1427, power: 0.2572
Test Batch 72/157, reward: 18.953, revenue_rate: 0.4962, distance: 8.4224, memory: 0.1859, power: 0.2549
Test Batch 73/157, reward: 16.724, revenue_rate: 0.4343, distance: 7.4339, memory: 0.1723, power: 0.2207
Test Batch 74/157, reward: 17.652, revenue_rate: 0.4525, distance: 7.3544, memory: 0.1333, power: 0.2240
Test Batch 75/157, reward: 17.200, revenue_rate: 0.4549, distance: 7.6873, memory: 0.1237, power: 0.2261
Test Batch 76/157, reward: 16.505, revenue_rate: 0.4350, distance: 7.4848, memory: 0.1509, power: 0.2233
Test Batch 77/157, reward: 15.789, revenue_rate: 0.4191, distance: 7.0020, memory: 0.1321, power: 0.2102
Test Batch 78/157, reward: 17.462, revenue_rate: 0.4561, distance: 7.7396, memory: 0.1226, power: 0.2240
Test Batch 79/157, reward: 19.894, revenue_rate: 0.5205, distance: 8.8434, memory: 0.1569, power: 0.2611
Test Batch 80/157, reward: 16.684, revenue_rate: 0.4372, distance: 7.2961, memory: 0.1552, power: 0.2187
Test Batch 81/157, reward: 17.335, revenue_rate: 0.4576, distance: 7.6889, memory: 0.1533, power: 0.2386
Test Batch 82/157, reward: 16.487, revenue_rate: 0.4329, distance: 7.1081, memory: 0.1302, power: 0.2183
Test Batch 83/157, reward: 19.534, revenue_rate: 0.5015, distance: 8.1796, memory: 0.1211, power: 0.2506
Test Batch 84/157, reward: 17.220, revenue_rate: 0.4521, distance: 7.4675, memory: 0.1287, power: 0.2266
Test Batch 85/157, reward: 17.600, revenue_rate: 0.4592, distance: 7.7189, memory: 0.0851, power: 0.2294
Test Batch 86/157, reward: 16.898, revenue_rate: 0.4424, distance: 7.3142, memory: 0.1491, power: 0.2221
Test Batch 87/157, reward: 19.617, revenue_rate: 0.5127, distance: 8.4171, memory: 0.1528, power: 0.2579
Test Batch 88/157, reward: 19.106, revenue_rate: 0.4985, distance: 8.2030, memory: 0.1358, power: 0.2451
Test Batch 89/157, reward: 16.869, revenue_rate: 0.4374, distance: 7.4164, memory: 0.1545, power: 0.2194
Test Batch 90/157, reward: 17.751, revenue_rate: 0.4644, distance: 7.8053, memory: 0.1496, power: 0.2311
Test Batch 91/157, reward: 18.328, revenue_rate: 0.4705, distance: 7.6653, memory: 0.1396, power: 0.2359
Test Batch 92/157, reward: 18.018, revenue_rate: 0.4679, distance: 7.7348, memory: 0.1432, power: 0.2399
Test Batch 93/157, reward: 18.539, revenue_rate: 0.4811, distance: 7.8781, memory: 0.1421, power: 0.2399
Test Batch 94/157, reward: 18.275, revenue_rate: 0.4769, distance: 7.9076, memory: 0.1188, power: 0.2330
Test Batch 95/157, reward: 18.631, revenue_rate: 0.4865, distance: 8.0317, memory: 0.1356, power: 0.2448
Test Batch 96/157, reward: 17.061, revenue_rate: 0.4490, distance: 7.5374, memory: 0.1510, power: 0.2303
Test Batch 97/157, reward: 17.491, revenue_rate: 0.4560, distance: 7.7122, memory: 0.1463, power: 0.2320
Test Batch 98/157, reward: 17.940, revenue_rate: 0.4688, distance: 7.9023, memory: 0.1356, power: 0.2409
Test Batch 99/157, reward: 17.442, revenue_rate: 0.4559, distance: 7.4628, memory: 0.1563, power: 0.2310
Test Batch 100/157, reward: 17.645, revenue_rate: 0.4576, distance: 7.7679, memory: 0.1332, power: 0.2335
Test Batch 101/157, reward: 16.503, revenue_rate: 0.4322, distance: 7.3617, memory: 0.1535, power: 0.2197
Test Batch 102/157, reward: 15.862, revenue_rate: 0.4223, distance: 7.1754, memory: 0.1263, power: 0.2157
Test Batch 103/157, reward: 19.006, revenue_rate: 0.4956, distance: 8.3173, memory: 0.1402, power: 0.2456
Test Batch 104/157, reward: 17.814, revenue_rate: 0.4633, distance: 7.7237, memory: 0.1137, power: 0.2339
Test Batch 105/157, reward: 17.071, revenue_rate: 0.4446, distance: 7.4739, memory: 0.1334, power: 0.2224
Test Batch 106/157, reward: 17.969, revenue_rate: 0.4685, distance: 7.6348, memory: 0.1197, power: 0.2347
Test Batch 107/157, reward: 17.752, revenue_rate: 0.4694, distance: 7.8744, memory: 0.1410, power: 0.2330
Test Batch 108/157, reward: 15.836, revenue_rate: 0.4142, distance: 6.8695, memory: 0.1617, power: 0.2128
Test Batch 109/157, reward: 17.389, revenue_rate: 0.4557, distance: 7.7375, memory: 0.1506, power: 0.2338
Test Batch 110/157, reward: 17.512, revenue_rate: 0.4555, distance: 7.5954, memory: 0.1138, power: 0.2231
Test Batch 111/157, reward: 17.637, revenue_rate: 0.4590, distance: 7.6294, memory: 0.1723, power: 0.2345
Test Batch 112/157, reward: 17.938, revenue_rate: 0.4632, distance: 7.5795, memory: 0.1234, power: 0.2342
Test Batch 113/157, reward: 17.764, revenue_rate: 0.4577, distance: 7.5747, memory: 0.0960, power: 0.2266
Test Batch 114/157, reward: 16.577, revenue_rate: 0.4345, distance: 7.0576, memory: 0.1447, power: 0.2159
Test Batch 115/157, reward: 15.870, revenue_rate: 0.4207, distance: 7.2994, memory: 0.1511, power: 0.2189
Test Batch 116/157, reward: 16.757, revenue_rate: 0.4422, distance: 7.4410, memory: 0.1347, power: 0.2232
Test Batch 117/157, reward: 16.429, revenue_rate: 0.4317, distance: 6.9643, memory: 0.1410, power: 0.2138
Test Batch 118/157, reward: 17.385, revenue_rate: 0.4566, distance: 7.5644, memory: 0.1453, power: 0.2270
Test Batch 119/157, reward: 17.214, revenue_rate: 0.4493, distance: 7.5687, memory: 0.1283, power: 0.2286
Test Batch 120/157, reward: 16.805, revenue_rate: 0.4366, distance: 7.2496, memory: 0.1436, power: 0.2274
Test Batch 121/157, reward: 19.727, revenue_rate: 0.5095, distance: 8.3906, memory: 0.1436, power: 0.2560
Test Batch 122/157, reward: 17.705, revenue_rate: 0.4602, distance: 7.7336, memory: 0.1515, power: 0.2295
Test Batch 123/157, reward: 17.639, revenue_rate: 0.4610, distance: 7.6943, memory: 0.1597, power: 0.2337
Test Batch 124/157, reward: 17.714, revenue_rate: 0.4629, distance: 7.7583, memory: 0.1316, power: 0.2381
Test Batch 125/157, reward: 17.005, revenue_rate: 0.4460, distance: 7.3417, memory: 0.1321, power: 0.2163
Test Batch 126/157, reward: 16.953, revenue_rate: 0.4485, distance: 7.5071, memory: 0.1019, power: 0.2255
Test Batch 127/157, reward: 17.222, revenue_rate: 0.4478, distance: 7.4013, memory: 0.1295, power: 0.2285
Test Batch 128/157, reward: 16.812, revenue_rate: 0.4361, distance: 7.2954, memory: 0.1379, power: 0.2184
Test Batch 129/157, reward: 17.496, revenue_rate: 0.4515, distance: 7.4605, memory: 0.1455, power: 0.2230
Test Batch 130/157, reward: 19.143, revenue_rate: 0.4926, distance: 8.2010, memory: 0.1570, power: 0.2499
Test Batch 131/157, reward: 16.905, revenue_rate: 0.4405, distance: 7.3805, memory: 0.1421, power: 0.2196
Test Batch 132/157, reward: 17.913, revenue_rate: 0.4759, distance: 8.2096, memory: 0.1355, power: 0.2419
Test Batch 133/157, reward: 18.662, revenue_rate: 0.4935, distance: 8.0893, memory: 0.1515, power: 0.2478
Test Batch 134/157, reward: 18.086, revenue_rate: 0.4673, distance: 7.6944, memory: 0.1154, power: 0.2313
Test Batch 135/157, reward: 17.716, revenue_rate: 0.4598, distance: 7.4294, memory: 0.1120, power: 0.2269
Test Batch 136/157, reward: 16.172, revenue_rate: 0.4218, distance: 7.0321, memory: 0.1151, power: 0.2110
Test Batch 137/157, reward: 17.506, revenue_rate: 0.4572, distance: 7.6423, memory: 0.1283, power: 0.2276
Test Batch 138/157, reward: 17.772, revenue_rate: 0.4658, distance: 7.9818, memory: 0.1198, power: 0.2296
Test Batch 139/157, reward: 18.178, revenue_rate: 0.4669, distance: 7.7950, memory: 0.1363, power: 0.2323
Test Batch 140/157, reward: 19.190, revenue_rate: 0.5018, distance: 8.4191, memory: 0.1367, power: 0.2493
Test Batch 141/157, reward: 18.298, revenue_rate: 0.4831, distance: 8.0102, memory: 0.1499, power: 0.2437
Test Batch 142/157, reward: 19.838, revenue_rate: 0.5217, distance: 8.8361, memory: 0.1785, power: 0.2616
Test Batch 143/157, reward: 18.084, revenue_rate: 0.4755, distance: 7.9175, memory: 0.1692, power: 0.2386
Test Batch 144/157, reward: 18.675, revenue_rate: 0.4898, distance: 8.2373, memory: 0.1875, power: 0.2442
Test Batch 145/157, reward: 19.159, revenue_rate: 0.5015, distance: 8.4325, memory: 0.1449, power: 0.2520
Test Batch 146/157, reward: 19.548, revenue_rate: 0.5140, distance: 8.7528, memory: 0.1518, power: 0.2530
Test Batch 147/157, reward: 17.179, revenue_rate: 0.4509, distance: 7.5659, memory: 0.1498, power: 0.2302
Test Batch 148/157, reward: 16.864, revenue_rate: 0.4361, distance: 7.4376, memory: 0.1658, power: 0.2240
Test Batch 149/157, reward: 16.769, revenue_rate: 0.4372, distance: 7.5245, memory: 0.1299, power: 0.2210
Test Batch 150/157, reward: 18.520, revenue_rate: 0.4798, distance: 7.7641, memory: 0.1346, power: 0.2422
Test Batch 151/157, reward: 17.045, revenue_rate: 0.4462, distance: 7.3240, memory: 0.1332, power: 0.2239
Test Batch 152/157, reward: 17.579, revenue_rate: 0.4625, distance: 7.8657, memory: 0.1270, power: 0.2308
Test Batch 153/157, reward: 16.821, revenue_rate: 0.4466, distance: 7.7788, memory: 0.1687, power: 0.2292
Test Batch 154/157, reward: 17.432, revenue_rate: 0.4521, distance: 7.2937, memory: 0.1666, power: 0.2292
Test Batch 155/157, reward: 16.686, revenue_rate: 0.4336, distance: 7.0054, memory: 0.0810, power: 0.2168
Test Batch 156/157, reward: 14.947, revenue_rate: 0.3904, distance: 6.3931, memory: 0.1653, power: 0.1975
Test Summary - Avg reward: 17.551, revenue_rate: 0.4583, distance: 7.6391, memory: 0.1403, power: 0.2308
验证完成 - Epoch 1, reward: 17.551, revenue_rate: 0.4583, distance: 7.6391, memory: 0.1403, power: 0.2308
已保存新模型到 constellation_smp\constellation_smp100\constellation_gpnindrnn_hybrid_transformer_L2H4_2025_08_19_16_22_30 (验证集奖励: 17.5507)

开始训练 Epoch 2/3
Epoch 2, Batch 10/1563, loss: 5.166, reward: 17.111, critic_reward: 16.238, revenue_rate: 0.4450, distance: 7.4272, memory: 0.1437, power: 0.2237, lr: 0.000100, took: 206.268s
Epoch 2, Batch 20/1563, loss: 5.316, reward: 16.632, critic_reward: 17.352, revenue_rate: 0.4342, distance: 7.2815, memory: 0.1345, power: 0.2189, lr: 0.000100, took: 193.496s
Epoch 2, Batch 30/1563, loss: 4.395, reward: 16.393, critic_reward: 16.854, revenue_rate: 0.4263, distance: 7.0710, memory: 0.1280, power: 0.2142, lr: 0.000100, took: 188.804s
Epoch 2, Batch 40/1563, loss: 3.917, reward: 16.193, critic_reward: 16.143, revenue_rate: 0.4196, distance: 6.8787, memory: 0.1202, power: 0.2100, lr: 0.000100, took: 192.850s
Epoch 2, Batch 50/1563, loss: 4.551, reward: 15.995, critic_reward: 16.126, revenue_rate: 0.4174, distance: 6.9502, memory: 0.1387, power: 0.2108, lr: 0.000100, took: 189.840s
Epoch 2, Batch 60/1563, loss: 5.243, reward: 17.154, critic_reward: 17.459, revenue_rate: 0.4488, distance: 7.4539, memory: 0.1440, power: 0.2278, lr: 0.000100, took: 199.535s
Epoch 2, Batch 70/1563, loss: 7.707, reward: 17.880, critic_reward: 16.848, revenue_rate: 0.4649, distance: 7.7880, memory: 0.1537, power: 0.2350, lr: 0.000100, took: 211.339s
Epoch 2, Batch 80/1563, loss: 4.718, reward: 17.839, critic_reward: 18.095, revenue_rate: 0.4662, distance: 7.7937, memory: 0.1467, power: 0.2349, lr: 0.000100, took: 209.510s
Epoch 2, Batch 90/1563, loss: 4.355, reward: 17.417, critic_reward: 17.858, revenue_rate: 0.4526, distance: 7.4906, memory: 0.1405, power: 0.2275, lr: 0.000100, took: 203.370s
Epoch 2, Batch 100/1563, loss: 6.320, reward: 17.536, critic_reward: 17.493, revenue_rate: 0.4554, distance: 7.5614, memory: 0.1382, power: 0.2288, lr: 0.000100, took: 201.540s
Epoch 2, Batch 110/1563, loss: 10.858, reward: 16.435, critic_reward: 16.753, revenue_rate: 0.4284, distance: 7.1046, memory: 0.1270, power: 0.2158, lr: 0.000100, took: 194.874s
Epoch 2, Batch 120/1563, loss: 9.923, reward: 16.216, critic_reward: 16.973, revenue_rate: 0.4236, distance: 7.0826, memory: 0.1131, power: 0.2134, lr: 0.000100, took: 195.195s
Epoch 2, Batch 130/1563, loss: 7.352, reward: 15.896, critic_reward: 14.916, revenue_rate: 0.4139, distance: 6.8679, memory: 0.1217, power: 0.2085, lr: 0.000100, took: 189.305s
Epoch 2, Batch 140/1563, loss: 7.046, reward: 16.732, critic_reward: 17.808, revenue_rate: 0.4365, distance: 7.3553, memory: 0.1410, power: 0.2207, lr: 0.000100, took: 202.521s
Epoch 2, Batch 150/1563, loss: 5.084, reward: 17.698, critic_reward: 17.173, revenue_rate: 0.4620, distance: 7.7512, memory: 0.1449, power: 0.2318, lr: 0.000100, took: 206.625s
Epoch 2, Batch 160/1563, loss: 9.267, reward: 17.126, critic_reward: 18.759, revenue_rate: 0.4466, distance: 7.4238, memory: 0.1350, power: 0.2250, lr: 0.000100, took: 204.025s
Epoch 2, Batch 170/1563, loss: 7.511, reward: 17.035, critic_reward: 16.139, revenue_rate: 0.4433, distance: 7.4097, memory: 0.1329, power: 0.2247, lr: 0.000100, took: 200.634s
Epoch 2, Batch 180/1563, loss: 6.033, reward: 16.004, critic_reward: 15.723, revenue_rate: 0.4199, distance: 7.0540, memory: 0.1335, power: 0.2136, lr: 0.000100, took: 194.886s
Epoch 2, Batch 190/1563, loss: 3.864, reward: 15.871, critic_reward: 16.122, revenue_rate: 0.4138, distance: 6.8748, memory: 0.1226, power: 0.2092, lr: 0.000100, took: 184.772s
Epoch 2, Batch 200/1563, loss: 4.871, reward: 15.412, critic_reward: 14.829, revenue_rate: 0.4003, distance: 6.6570, memory: 0.1072, power: 0.2012, lr: 0.000100, took: 175.882s
Epoch 2, Batch 210/1563, loss: 4.501, reward: 14.796, critic_reward: 14.892, revenue_rate: 0.3855, distance: 6.3909, memory: 0.0998, power: 0.1938, lr: 0.000100, took: 173.313s
Epoch 2, Batch 220/1563, loss: 4.263, reward: 14.441, critic_reward: 14.307, revenue_rate: 0.3749, distance: 6.1827, memory: 0.0925, power: 0.1864, lr: 0.000100, took: 165.191s
Epoch 2, Batch 230/1563, loss: 5.069, reward: 15.168, critic_reward: 15.804, revenue_rate: 0.3951, distance: 6.5520, memory: 0.0862, power: 0.1967, lr: 0.000100, took: 165.667s
Epoch 2, Batch 240/1563, loss: 8.436, reward: 14.715, critic_reward: 13.824, revenue_rate: 0.3823, distance: 6.2673, memory: 0.0688, power: 0.1867, lr: 0.000100, took: 161.991s
Epoch 2, Batch 250/1563, loss: 7.466, reward: 14.338, critic_reward: 15.071, revenue_rate: 0.3716, distance: 6.0653, memory: 0.0762, power: 0.1830, lr: 0.000100, took: 164.584s
Epoch 2, Batch 260/1563, loss: 6.485, reward: 14.310, critic_reward: 13.731, revenue_rate: 0.3718, distance: 6.0531, memory: 0.0740, power: 0.1830, lr: 0.000100, took: 162.714s
Epoch 2, Batch 270/1563, loss: 10.124, reward: 14.601, critic_reward: 14.764, revenue_rate: 0.3817, distance: 6.3125, memory: 0.0878, power: 0.1896, lr: 0.000100, took: 168.944s
Epoch 2, Batch 280/1563, loss: 5.709, reward: 15.338, critic_reward: 14.972, revenue_rate: 0.3996, distance: 6.5918, memory: 0.0851, power: 0.1990, lr: 0.000100, took: 172.122s
Epoch 2, Batch 290/1563, loss: 5.239, reward: 15.474, critic_reward: 15.890, revenue_rate: 0.4024, distance: 6.6130, memory: 0.0836, power: 0.2002, lr: 0.000100, took: 175.990s
Epoch 2, Batch 300/1563, loss: 3.505, reward: 15.211, critic_reward: 15.195, revenue_rate: 0.3951, distance: 6.4452, memory: 0.0958, power: 0.1965, lr: 0.000100, took: 179.971s
Epoch 2, Batch 310/1563, loss: 5.296, reward: 16.143, critic_reward: 16.432, revenue_rate: 0.4203, distance: 6.9202, memory: 0.0982, power: 0.2093, lr: 0.000100, took: 186.397s
Epoch 2, Batch 320/1563, loss: 7.387, reward: 16.048, critic_reward: 15.927, revenue_rate: 0.4186, distance: 6.9304, memory: 0.1162, power: 0.2096, lr: 0.000100, took: 189.082s
Epoch 2, Batch 330/1563, loss: 5.739, reward: 16.161, critic_reward: 15.706, revenue_rate: 0.4194, distance: 6.8207, memory: 0.0863, power: 0.2079, lr: 0.000100, took: 186.168s
Epoch 2, Batch 340/1563, loss: 6.891, reward: 16.065, critic_reward: 17.129, revenue_rate: 0.4193, distance: 6.8796, memory: 0.1071, power: 0.2091, lr: 0.000100, took: 189.974s
Epoch 2, Batch 350/1563, loss: 8.817, reward: 16.485, critic_reward: 14.758, revenue_rate: 0.4305, distance: 7.1560, memory: 0.1159, power: 0.2170, lr: 0.000100, took: 185.535s
Epoch 2, Batch 360/1563, loss: 4.341, reward: 15.740, critic_reward: 15.416, revenue_rate: 0.4107, distance: 6.9035, memory: 0.1185, power: 0.2065, lr: 0.000100, took: 192.930s
Epoch 2, Batch 370/1563, loss: 4.335, reward: 16.087, critic_reward: 16.033, revenue_rate: 0.4200, distance: 7.0035, memory: 0.1264, power: 0.2111, lr: 0.000100, took: 187.041s
Epoch 2, Batch 380/1563, loss: 9.471, reward: 16.152, critic_reward: 15.982, revenue_rate: 0.4196, distance: 6.9602, memory: 0.1292, power: 0.2132, lr: 0.000100, took: 188.254s
Epoch 2, Batch 390/1563, loss: 7.373, reward: 15.742, critic_reward: 15.796, revenue_rate: 0.4114, distance: 6.9704, memory: 0.1315, power: 0.2080, lr: 0.000100, took: 189.346s
Epoch 2, Batch 400/1563, loss: 8.714, reward: 16.152, critic_reward: 16.211, revenue_rate: 0.4190, distance: 7.0166, memory: 0.1323, power: 0.2134, lr: 0.000100, took: 189.656s
Epoch 2, Batch 410/1563, loss: 5.189, reward: 17.235, critic_reward: 17.133, revenue_rate: 0.4485, distance: 7.4949, memory: 0.1334, power: 0.2257, lr: 0.000100, took: 201.619s
Epoch 2, Batch 420/1563, loss: 4.479, reward: 16.729, critic_reward: 17.210, revenue_rate: 0.4350, distance: 7.2409, memory: 0.1410, power: 0.2191, lr: 0.000100, took: 199.140s
Epoch 2, Batch 430/1563, loss: 5.175, reward: 16.693, critic_reward: 16.717, revenue_rate: 0.4354, distance: 7.3052, memory: 0.1354, power: 0.2218, lr: 0.000100, took: 200.127s
Epoch 2, Batch 440/1563, loss: 5.235, reward: 16.865, critic_reward: 15.981, revenue_rate: 0.4402, distance: 7.3699, memory: 0.1410, power: 0.2239, lr: 0.000100, took: 195.516s
Epoch 2, Batch 450/1563, loss: 5.564, reward: 17.255, critic_reward: 18.132, revenue_rate: 0.4511, distance: 7.6063, memory: 0.1534, power: 0.2277, lr: 0.000100, took: 205.587s
Epoch 2, Batch 460/1563, loss: 4.663, reward: 16.966, critic_reward: 17.090, revenue_rate: 0.4418, distance: 7.3641, memory: 0.1371, power: 0.2248, lr: 0.000100, took: 201.549s
Epoch 2, Batch 470/1563, loss: 5.179, reward: 16.528, critic_reward: 17.043, revenue_rate: 0.4324, distance: 7.2421, memory: 0.1371, power: 0.2198, lr: 0.000100, took: 199.033s
Epoch 2, Batch 480/1563, loss: 4.894, reward: 15.984, critic_reward: 16.041, revenue_rate: 0.4169, distance: 6.9656, memory: 0.1294, power: 0.2108, lr: 0.000100, took: 192.213s
Epoch 2, Batch 490/1563, loss: 3.938, reward: 15.638, critic_reward: 15.392, revenue_rate: 0.4082, distance: 6.7539, memory: 0.1238, power: 0.2049, lr: 0.000100, took: 182.907s
Epoch 2, Batch 500/1563, loss: 5.419, reward: 15.261, critic_reward: 15.398, revenue_rate: 0.3992, distance: 6.6303, memory: 0.1091, power: 0.2012, lr: 0.000100, took: 183.726s
Epoch 2, Batch 510/1563, loss: 4.407, reward: 15.087, critic_reward: 14.703, revenue_rate: 0.3939, distance: 6.5737, memory: 0.1099, power: 0.1979, lr: 0.000100, took: 179.740s
Epoch 2, Batch 520/1563, loss: 6.715, reward: 15.471, critic_reward: 16.574, revenue_rate: 0.4058, distance: 6.8659, memory: 0.1221, power: 0.2065, lr: 0.000100, took: 182.771s
Epoch 2, Batch 530/1563, loss: 4.148, reward: 15.765, critic_reward: 15.417, revenue_rate: 0.4118, distance: 6.7827, memory: 0.1085, power: 0.2070, lr: 0.000100, took: 178.394s
Epoch 2, Batch 540/1563, loss: 5.791, reward: 15.649, critic_reward: 14.991, revenue_rate: 0.4067, distance: 6.6792, memory: 0.1199, power: 0.2035, lr: 0.000100, took: 180.715s
Epoch 2, Batch 550/1563, loss: 4.530, reward: 15.994, critic_reward: 16.484, revenue_rate: 0.4178, distance: 6.9361, memory: 0.1194, power: 0.2098, lr: 0.000100, took: 187.555s
Epoch 2, Batch 560/1563, loss: 4.900, reward: 16.141, critic_reward: 16.208, revenue_rate: 0.4193, distance: 6.8914, memory: 0.1180, power: 0.2120, lr: 0.000100, took: 190.128s
Epoch 2, Batch 570/1563, loss: 5.942, reward: 16.289, critic_reward: 16.635, revenue_rate: 0.4255, distance: 7.1196, memory: 0.1194, power: 0.2154, lr: 0.000100, took: 190.535s
Epoch 2, Batch 580/1563, loss: 4.821, reward: 16.004, critic_reward: 16.167, revenue_rate: 0.4156, distance: 6.7748, memory: 0.1062, power: 0.2080, lr: 0.000100, took: 184.118s
Epoch 2, Batch 590/1563, loss: 5.447, reward: 15.312, critic_reward: 15.339, revenue_rate: 0.3974, distance: 6.5548, memory: 0.1039, power: 0.1982, lr: 0.000100, took: 177.964s
Epoch 2, Batch 600/1563, loss: 5.844, reward: 14.889, critic_reward: 15.676, revenue_rate: 0.3892, distance: 6.5437, memory: 0.1162, power: 0.1964, lr: 0.000100, took: 175.910s
Epoch 2, Batch 610/1563, loss: 5.934, reward: 14.795, critic_reward: 13.792, revenue_rate: 0.3849, distance: 6.4026, memory: 0.1008, power: 0.1944, lr: 0.000100, took: 169.565s
Epoch 2, Batch 620/1563, loss: 4.345, reward: 14.401, critic_reward: 15.054, revenue_rate: 0.3750, distance: 6.1806, memory: 0.0979, power: 0.1865, lr: 0.000100, took: 166.517s
Epoch 2, Batch 630/1563, loss: 4.754, reward: 14.026, critic_reward: 13.695, revenue_rate: 0.3644, distance: 5.9727, memory: 0.0965, power: 0.1824, lr: 0.000100, took: 162.288s
Epoch 2, Batch 640/1563, loss: 7.632, reward: 13.627, critic_reward: 14.134, revenue_rate: 0.3572, distance: 6.0181, memory: 0.1120, power: 0.1801, lr: 0.000100, took: 162.798s
Epoch 2, Batch 650/1563, loss: 4.958, reward: 14.217, critic_reward: 14.038, revenue_rate: 0.3700, distance: 6.1240, memory: 0.0926, power: 0.1841, lr: 0.000100, took: 164.947s
Epoch 2, Batch 660/1563, loss: 3.399, reward: 13.872, critic_reward: 13.655, revenue_rate: 0.3625, distance: 6.0341, memory: 0.1075, power: 0.1829, lr: 0.000100, took: 165.753s
Epoch 2, Batch 670/1563, loss: 5.898, reward: 13.787, critic_reward: 14.617, revenue_rate: 0.3584, distance: 5.9523, memory: 0.0953, power: 0.1800, lr: 0.000100, took: 153.724s
Epoch 2, Batch 680/1563, loss: 7.402, reward: 13.405, critic_reward: 11.783, revenue_rate: 0.3497, distance: 5.8672, memory: 0.1057, power: 0.1778, lr: 0.000100, took: 156.762s
Epoch 2, Batch 690/1563, loss: 6.689, reward: 13.970, critic_reward: 14.945, revenue_rate: 0.3638, distance: 6.0737, memory: 0.0946, power: 0.1817, lr: 0.000100, took: 155.964s
Epoch 2, Batch 700/1563, loss: 4.922, reward: 14.153, critic_reward: 14.921, revenue_rate: 0.3690, distance: 6.1361, memory: 0.0988, power: 0.1834, lr: 0.000100, took: 161.177s
Epoch 2, Batch 710/1563, loss: 8.114, reward: 14.231, critic_reward: 12.397, revenue_rate: 0.3684, distance: 6.0165, memory: 0.0929, power: 0.1837, lr: 0.000100, took: 161.734s
Epoch 2, Batch 720/1563, loss: 4.908, reward: 14.746, critic_reward: 15.276, revenue_rate: 0.3837, distance: 6.3881, memory: 0.1066, power: 0.1938, lr: 0.000100, took: 168.950s
Epoch 2, Batch 730/1563, loss: 4.873, reward: 13.677, critic_reward: 13.439, revenue_rate: 0.3569, distance: 6.0200, memory: 0.1204, power: 0.1815, lr: 0.000100, took: 162.352s
Epoch 2, Batch 740/1563, loss: 6.265, reward: 14.335, critic_reward: 14.075, revenue_rate: 0.3736, distance: 6.1833, memory: 0.0965, power: 0.1856, lr: 0.000100, took: 166.133s
Epoch 2, Batch 750/1563, loss: 6.386, reward: 14.293, critic_reward: 14.322, revenue_rate: 0.3724, distance: 6.1881, memory: 0.1073, power: 0.1879, lr: 0.000100, took: 163.003s
Epoch 2, Batch 760/1563, loss: 5.194, reward: 14.399, critic_reward: 14.995, revenue_rate: 0.3768, distance: 6.2815, memory: 0.1060, power: 0.1911, lr: 0.000100, took: 159.887s
Epoch 2, Batch 770/1563, loss: 4.235, reward: 14.954, critic_reward: 14.965, revenue_rate: 0.3906, distance: 6.5358, memory: 0.1244, power: 0.1992, lr: 0.000100, took: 172.625s
Epoch 2, Batch 780/1563, loss: 3.944, reward: 15.150, critic_reward: 15.031, revenue_rate: 0.3957, distance: 6.6209, memory: 0.1191, power: 0.1990, lr: 0.000100, took: 173.507s
Epoch 2, Batch 790/1563, loss: 5.888, reward: 15.133, critic_reward: 15.253, revenue_rate: 0.3947, distance: 6.5422, memory: 0.1214, power: 0.1994, lr: 0.000100, took: 175.002s
Epoch 2, Batch 800/1563, loss: 4.939, reward: 14.914, critic_reward: 14.813, revenue_rate: 0.3893, distance: 6.4518, memory: 0.1176, power: 0.1973, lr: 0.000100, took: 177.098s
Epoch 2, Batch 810/1563, loss: 4.488, reward: 14.731, critic_reward: 14.832, revenue_rate: 0.3825, distance: 6.3026, memory: 0.1084, power: 0.1907, lr: 0.000100, took: 170.157s
Epoch 2, Batch 820/1563, loss: 5.576, reward: 15.143, critic_reward: 15.595, revenue_rate: 0.3937, distance: 6.4815, memory: 0.0976, power: 0.1963, lr: 0.000100, took: 175.452s
Epoch 2, Batch 830/1563, loss: 7.375, reward: 15.557, critic_reward: 14.070, revenue_rate: 0.4060, distance: 6.7359, memory: 0.1004, power: 0.2025, lr: 0.000100, took: 177.698s
Epoch 2, Batch 840/1563, loss: 5.043, reward: 16.417, critic_reward: 17.436, revenue_rate: 0.4286, distance: 7.2449, memory: 0.1203, power: 0.2161, lr: 0.000100, took: 187.857s
Epoch 2, Batch 850/1563, loss: 4.151, reward: 16.236, critic_reward: 16.065, revenue_rate: 0.4229, distance: 7.0003, memory: 0.1190, power: 0.2126, lr: 0.000100, took: 184.618s
Epoch 2, Batch 860/1563, loss: 5.120, reward: 16.554, critic_reward: 16.821, revenue_rate: 0.4315, distance: 7.1317, memory: 0.1133, power: 0.2148, lr: 0.000100, took: 192.311s
Epoch 2, Batch 870/1563, loss: 3.776, reward: 16.727, critic_reward: 16.835, revenue_rate: 0.4362, distance: 7.3025, memory: 0.1359, power: 0.2220, lr: 0.000100, took: 196.560s
Epoch 2, Batch 880/1563, loss: 5.339, reward: 17.213, critic_reward: 16.378, revenue_rate: 0.4489, distance: 7.5092, memory: 0.1389, power: 0.2274, lr: 0.000100, took: 202.526s
Epoch 2, Batch 890/1563, loss: 8.766, reward: 15.966, critic_reward: 17.470, revenue_rate: 0.4177, distance: 6.9554, memory: 0.1083, power: 0.2096, lr: 0.000100, took: 189.903s
Epoch 2, Batch 900/1563, loss: 8.165, reward: 16.210, critic_reward: 15.373, revenue_rate: 0.4236, distance: 7.0930, memory: 0.1240, power: 0.2133, lr: 0.000100, took: 189.973s
Epoch 2, Batch 910/1563, loss: 6.723, reward: 15.381, critic_reward: 16.685, revenue_rate: 0.4021, distance: 6.7165, memory: 0.1286, power: 0.2042, lr: 0.000100, took: 185.885s
Epoch 2, Batch 920/1563, loss: 5.403, reward: 15.774, critic_reward: 15.457, revenue_rate: 0.4130, distance: 6.9172, memory: 0.1217, power: 0.2081, lr: 0.000100, took: 185.981s
Epoch 2, Batch 930/1563, loss: 6.073, reward: 15.708, critic_reward: 15.864, revenue_rate: 0.4101, distance: 6.8178, memory: 0.1255, power: 0.2079, lr: 0.000100, took: 187.790s
Epoch 2, Batch 940/1563, loss: 5.786, reward: 16.348, critic_reward: 16.315, revenue_rate: 0.4263, distance: 7.0445, memory: 0.1189, power: 0.2136, lr: 0.000100, took: 186.374s
Epoch 2, Batch 950/1563, loss: 4.494, reward: 16.421, critic_reward: 16.339, revenue_rate: 0.4265, distance: 7.0683, memory: 0.1008, power: 0.2139, lr: 0.000100, took: 187.428s
Epoch 2, Batch 960/1563, loss: 3.836, reward: 15.523, critic_reward: 15.694, revenue_rate: 0.4035, distance: 6.6567, memory: 0.1095, power: 0.2018, lr: 0.000100, took: 182.170s
Epoch 2, Batch 970/1563, loss: 4.781, reward: 15.258, critic_reward: 15.835, revenue_rate: 0.3962, distance: 6.5801, memory: 0.1062, power: 0.1995, lr: 0.000100, took: 177.721s
Epoch 2, Batch 980/1563, loss: 3.805, reward: 15.300, critic_reward: 15.015, revenue_rate: 0.3986, distance: 6.6096, memory: 0.1043, power: 0.1993, lr: 0.000100, took: 178.218s
Epoch 2, Batch 990/1563, loss: 4.499, reward: 15.663, critic_reward: 15.811, revenue_rate: 0.4090, distance: 6.8003, memory: 0.1123, power: 0.2051, lr: 0.000100, took: 189.060s
Epoch 2, Batch 1000/1563, loss: 5.932, reward: 15.716, critic_reward: 15.181, revenue_rate: 0.4093, distance: 6.7723, memory: 0.1067, power: 0.2052, lr: 0.000100, took: 183.955s
Epoch 2, Batch 1010/1563, loss: 5.166, reward: 15.159, critic_reward: 15.730, revenue_rate: 0.3937, distance: 6.4515, memory: 0.0941, power: 0.1962, lr: 0.000100, took: 178.741s
Epoch 2, Batch 1020/1563, loss: 5.894, reward: 13.661, critic_reward: 13.841, revenue_rate: 0.3551, distance: 5.8421, memory: 0.0811, power: 0.1766, lr: 0.000100, took: 156.567s
Epoch 2, Batch 1030/1563, loss: 4.333, reward: 13.536, critic_reward: 13.403, revenue_rate: 0.3508, distance: 5.7161, memory: 0.0715, power: 0.1744, lr: 0.000100, took: 152.398s
Epoch 2, Batch 1040/1563, loss: 3.686, reward: 13.070, critic_reward: 12.999, revenue_rate: 0.3383, distance: 5.5245, memory: 0.0667, power: 0.1685, lr: 0.000100, took: 145.084s
Epoch 2, Batch 1050/1563, loss: 3.762, reward: 12.914, critic_reward: 12.795, revenue_rate: 0.3354, distance: 5.4879, memory: 0.0637, power: 0.1657, lr: 0.000100, took: 141.541s
Epoch 2, Batch 1060/1563, loss: 4.240, reward: 13.890, critic_reward: 13.424, revenue_rate: 0.3627, distance: 5.9160, memory: 0.0812, power: 0.1791, lr: 0.000100, took: 160.538s
Epoch 2, Batch 1070/1563, loss: 10.112, reward: 15.876, critic_reward: 15.114, revenue_rate: 0.4153, distance: 6.9179, memory: 0.0994, power: 0.2074, lr: 0.000100, took: 184.806s
Epoch 2, Batch 1080/1563, loss: 6.949, reward: 15.316, critic_reward: 14.745, revenue_rate: 0.4014, distance: 6.7273, memory: 0.1097, power: 0.2031, lr: 0.000100, took: 176.661s
Epoch 2, Batch 1090/1563, loss: 4.663, reward: 15.244, critic_reward: 15.148, revenue_rate: 0.3972, distance: 6.5942, memory: 0.1095, power: 0.1975, lr: 0.000100, took: 175.863s
Epoch 2, Batch 1100/1563, loss: 4.231, reward: 17.369, critic_reward: 17.012, revenue_rate: 0.4526, distance: 7.5352, memory: 0.1412, power: 0.2288, lr: 0.000100, took: 204.127s
Epoch 2, Batch 1110/1563, loss: 5.743, reward: 18.811, critic_reward: 18.366, revenue_rate: 0.4877, distance: 8.0749, memory: 0.1302, power: 0.2447, lr: 0.000100, took: 216.957s
Epoch 2, Batch 1120/1563, loss: 4.522, reward: 19.246, critic_reward: 19.469, revenue_rate: 0.5019, distance: 8.3274, memory: 0.1412, power: 0.2519, lr: 0.000100, took: 229.590s
Epoch 2, Batch 1130/1563, loss: 8.659, reward: 20.383, critic_reward: 19.461, revenue_rate: 0.5289, distance: 8.7265, memory: 0.1252, power: 0.2637, lr: 0.000100, took: 237.940s
Epoch 2, Batch 1140/1563, loss: 5.204, reward: 20.705, critic_reward: 20.531, revenue_rate: 0.5410, distance: 8.9973, memory: 0.1383, power: 0.2701, lr: 0.000100, took: 244.890s
Epoch 2, Batch 1150/1563, loss: 7.372, reward: 19.475, critic_reward: 20.593, revenue_rate: 0.5048, distance: 8.3081, memory: 0.1151, power: 0.2521, lr: 0.000100, took: 229.169s
Epoch 2, Batch 1160/1563, loss: 6.016, reward: 18.853, critic_reward: 18.808, revenue_rate: 0.4902, distance: 8.1678, memory: 0.1242, power: 0.2478, lr: 0.000100, took: 222.105s
Epoch 2, Batch 1170/1563, loss: 6.515, reward: 18.867, critic_reward: 19.338, revenue_rate: 0.4929, distance: 8.1764, memory: 0.1254, power: 0.2471, lr: 0.000100, took: 225.708s
Epoch 2, Batch 1180/1563, loss: 4.957, reward: 19.658, critic_reward: 19.931, revenue_rate: 0.5125, distance: 8.4505, memory: 0.1156, power: 0.2544, lr: 0.000100, took: 226.085s
Epoch 2, Batch 1190/1563, loss: 8.115, reward: 19.729, critic_reward: 19.658, revenue_rate: 0.5157, distance: 8.5439, memory: 0.1189, power: 0.2584, lr: 0.000100, took: 228.198s
Epoch 2, Batch 1200/1563, loss: 15.692, reward: 17.935, critic_reward: 17.736, revenue_rate: 0.4657, distance: 7.6847, memory: 0.1022, power: 0.2338, lr: 0.000100, took: 211.651s
Epoch 2, Batch 1210/1563, loss: 11.877, reward: 18.033, critic_reward: 17.311, revenue_rate: 0.4687, distance: 7.7026, memory: 0.1161, power: 0.2335, lr: 0.000100, took: 207.969s
Epoch 2, Batch 1220/1563, loss: 7.071, reward: 18.015, critic_reward: 18.197, revenue_rate: 0.4675, distance: 7.7079, memory: 0.0999, power: 0.2318, lr: 0.000100, took: 211.880s
Epoch 2, Batch 1230/1563, loss: 4.666, reward: 18.027, critic_reward: 18.225, revenue_rate: 0.4679, distance: 7.6894, memory: 0.1133, power: 0.2347, lr: 0.000100, took: 212.221s
Epoch 2, Batch 1240/1563, loss: 5.833, reward: 17.984, critic_reward: 18.134, revenue_rate: 0.4678, distance: 7.7324, memory: 0.1169, power: 0.2348, lr: 0.000100, took: 211.898s
Epoch 2, Batch 1250/1563, loss: 5.129, reward: 18.757, critic_reward: 18.805, revenue_rate: 0.4860, distance: 7.9970, memory: 0.1196, power: 0.2431, lr: 0.000100, took: 215.169s
Epoch 2, Batch 1260/1563, loss: 5.701, reward: 18.408, critic_reward: 17.995, revenue_rate: 0.4798, distance: 7.9871, memory: 0.1263, power: 0.2419, lr: 0.000100, took: 212.115s
Epoch 2, Batch 1270/1563, loss: 5.542, reward: 16.906, critic_reward: 17.396, revenue_rate: 0.4414, distance: 7.3290, memory: 0.1274, power: 0.2220, lr: 0.000100, took: 202.905s
Epoch 2, Batch 1280/1563, loss: 3.962, reward: 16.160, critic_reward: 16.169, revenue_rate: 0.4202, distance: 6.9680, memory: 0.1238, power: 0.2120, lr: 0.000100, took: 194.745s
Epoch 2, Batch 1290/1563, loss: 5.142, reward: 15.975, critic_reward: 16.224, revenue_rate: 0.4156, distance: 6.9428, memory: 0.1217, power: 0.2098, lr: 0.000100, took: 183.988s
Epoch 2, Batch 1300/1563, loss: 5.368, reward: 13.625, critic_reward: 13.514, revenue_rate: 0.3536, distance: 5.7973, memory: 0.0697, power: 0.1775, lr: 0.000100, took: 153.805s
Epoch 2, Batch 1310/1563, loss: 3.960, reward: 12.154, critic_reward: 12.671, revenue_rate: 0.3147, distance: 5.0919, memory: 0.0632, power: 0.1553, lr: 0.000100, took: 132.959s
Epoch 2, Batch 1320/1563, loss: 2.871, reward: 11.510, critic_reward: 11.630, revenue_rate: 0.2973, distance: 4.8062, memory: 0.0452, power: 0.1462, lr: 0.000100, took: 125.913s
Epoch 2, Batch 1330/1563, loss: 3.921, reward: 11.510, critic_reward: 11.397, revenue_rate: 0.2989, distance: 4.9163, memory: 0.0651, power: 0.1481, lr: 0.000100, took: 127.619s
Epoch 2, Batch 1340/1563, loss: 3.752, reward: 12.089, critic_reward: 12.257, revenue_rate: 0.3125, distance: 5.0486, memory: 0.0442, power: 0.1534, lr: 0.000100, took: 130.767s
Epoch 2, Batch 1350/1563, loss: 3.765, reward: 11.746, critic_reward: 11.450, revenue_rate: 0.3025, distance: 4.9297, memory: 0.0568, power: 0.1503, lr: 0.000100, took: 127.564s
Epoch 2, Batch 1360/1563, loss: 3.597, reward: 11.641, critic_reward: 11.660, revenue_rate: 0.3012, distance: 4.8772, memory: 0.0487, power: 0.1478, lr: 0.000100, took: 125.564s
Epoch 2, Batch 1370/1563, loss: 4.079, reward: 11.520, critic_reward: 11.468, revenue_rate: 0.2975, distance: 4.7872, memory: 0.0460, power: 0.1463, lr: 0.000100, took: 126.398s
Epoch 2, Batch 1380/1563, loss: 4.677, reward: 11.488, critic_reward: 10.641, revenue_rate: 0.2979, distance: 4.8630, memory: 0.0559, power: 0.1468, lr: 0.000100, took: 129.458s
Epoch 2, Batch 1390/1563, loss: 2.944, reward: 11.158, critic_reward: 11.273, revenue_rate: 0.2900, distance: 4.7189, memory: 0.0418, power: 0.1433, lr: 0.000100, took: 121.504s
Epoch 2, Batch 1400/1563, loss: 3.682, reward: 11.673, critic_reward: 11.559, revenue_rate: 0.3010, distance: 4.8275, memory: 0.0402, power: 0.1480, lr: 0.000100, took: 124.065s
Epoch 2, Batch 1410/1563, loss: 5.702, reward: 11.168, critic_reward: 11.069, revenue_rate: 0.2891, distance: 4.6620, memory: 0.0399, power: 0.1414, lr: 0.000100, took: 127.407s
Epoch 2, Batch 1420/1563, loss: 6.513, reward: 11.212, critic_reward: 10.496, revenue_rate: 0.2898, distance: 4.6762, memory: 0.0395, power: 0.1432, lr: 0.000100, took: 123.580s
Epoch 2, Batch 1430/1563, loss: 5.307, reward: 11.395, critic_reward: 11.437, revenue_rate: 0.2955, distance: 4.7983, memory: 0.0527, power: 0.1454, lr: 0.000100, took: 126.317s
Epoch 2, Batch 1440/1563, loss: 5.852, reward: 11.286, critic_reward: 11.035, revenue_rate: 0.2913, distance: 4.7064, memory: 0.0502, power: 0.1440, lr: 0.000100, took: 127.069s
Epoch 2, Batch 1450/1563, loss: 6.004, reward: 11.772, critic_reward: 10.516, revenue_rate: 0.3030, distance: 4.8212, memory: 0.0501, power: 0.1470, lr: 0.000100, took: 126.042s
Epoch 2, Batch 1460/1563, loss: 14.221, reward: 10.915, critic_reward: 13.827, revenue_rate: 0.2832, distance: 4.5666, memory: 0.0548, power: 0.1415, lr: 0.000100, took: 123.337s
Epoch 2, Batch 1470/1563, loss: 4.590, reward: 11.596, critic_reward: 10.673, revenue_rate: 0.3006, distance: 4.8457, memory: 0.0465, power: 0.1463, lr: 0.000100, took: 127.165s
Epoch 2, Batch 1480/1563, loss: 5.729, reward: 11.113, critic_reward: 12.577, revenue_rate: 0.2872, distance: 4.6251, memory: 0.0484, power: 0.1413, lr: 0.000100, took: 126.338s
Epoch 2, Batch 1490/1563, loss: 4.838, reward: 11.310, critic_reward: 10.417, revenue_rate: 0.2921, distance: 4.7063, memory: 0.0446, power: 0.1440, lr: 0.000100, took: 125.399s
Epoch 2, Batch 1500/1563, loss: 3.232, reward: 11.096, critic_reward: 11.134, revenue_rate: 0.2874, distance: 4.6224, memory: 0.0450, power: 0.1413, lr: 0.000100, took: 121.474s
Epoch 2, Batch 1510/1563, loss: 3.941, reward: 11.087, critic_reward: 11.258, revenue_rate: 0.2862, distance: 4.6577, memory: 0.0448, power: 0.1418, lr: 0.000100, took: 122.410s
Epoch 2, Batch 1520/1563, loss: 6.128, reward: 11.114, critic_reward: 10.366, revenue_rate: 0.2889, distance: 4.7030, memory: 0.0465, power: 0.1429, lr: 0.000100, took: 125.375s
Epoch 2, Batch 1530/1563, loss: 3.429, reward: 11.002, critic_reward: 11.382, revenue_rate: 0.2852, distance: 4.6545, memory: 0.0426, power: 0.1417, lr: 0.000100, took: 121.905s
Epoch 2, Batch 1540/1563, loss: 3.660, reward: 11.041, critic_reward: 11.248, revenue_rate: 0.2865, distance: 4.6727, memory: 0.0387, power: 0.1400, lr: 0.000100, took: 122.202s
Epoch 2, Batch 1550/1563, loss: 4.673, reward: 11.243, critic_reward: 10.358, revenue_rate: 0.2918, distance: 4.7565, memory: 0.0497, power: 0.1444, lr: 0.000100, took: 123.167s
Epoch 2, Batch 1560/1563, loss: 2.590, reward: 11.150, critic_reward: 11.336, revenue_rate: 0.2873, distance: 4.6077, memory: 0.0368, power: 0.1390, lr: 0.000100, took: 121.460s
开始验证...
Test Batch 0/157, reward: 10.494, revenue_rate: 0.2718, distance: 4.3130, memory: 0.0481, power: 0.1346
Test Batch 1/157, reward: 11.701, revenue_rate: 0.3033, distance: 4.9122, memory: 0.0483, power: 0.1496
Test Batch 2/157, reward: 11.276, revenue_rate: 0.2907, distance: 4.4966, memory: 0.0236, power: 0.1370
Test Batch 3/157, reward: 10.372, revenue_rate: 0.2710, distance: 4.3900, memory: 0.0290, power: 0.1340
Test Batch 4/157, reward: 11.096, revenue_rate: 0.2851, distance: 4.5270, memory: 0.0080, power: 0.1390
Test Batch 5/157, reward: 11.079, revenue_rate: 0.2844, distance: 4.6407, memory: 0.0399, power: 0.1399
Test Batch 6/157, reward: 12.263, revenue_rate: 0.3147, distance: 4.8493, memory: 0.0096, power: 0.1497
Test Batch 7/157, reward: 11.311, revenue_rate: 0.2943, distance: 4.7357, memory: 0.0481, power: 0.1468
Test Batch 8/157, reward: 11.722, revenue_rate: 0.3066, distance: 5.0505, memory: 0.0374, power: 0.1488
Test Batch 9/157, reward: 11.304, revenue_rate: 0.2882, distance: 4.4910, memory: 0.0179, power: 0.1399
Test Batch 10/157, reward: 10.661, revenue_rate: 0.2799, distance: 4.5541, memory: 0.0501, power: 0.1389
Test Batch 11/157, reward: 11.690, revenue_rate: 0.2982, distance: 4.7954, memory: 0.0352, power: 0.1404
Test Batch 12/157, reward: 11.029, revenue_rate: 0.2876, distance: 4.8342, memory: 0.0601, power: 0.1438
Test Batch 13/157, reward: 10.710, revenue_rate: 0.2792, distance: 4.5606, memory: 0.0653, power: 0.1356
Test Batch 14/157, reward: 11.056, revenue_rate: 0.2872, distance: 4.6709, memory: 0.0325, power: 0.1400
Test Batch 15/157, reward: 12.700, revenue_rate: 0.3257, distance: 5.1424, memory: 0.0381, power: 0.1593
Test Batch 16/157, reward: 11.771, revenue_rate: 0.3040, distance: 5.0365, memory: 0.1030, power: 0.1561
Test Batch 17/157, reward: 11.447, revenue_rate: 0.2944, distance: 4.6383, memory: 0.0556, power: 0.1466
Test Batch 18/157, reward: 11.195, revenue_rate: 0.2857, distance: 4.4553, memory: 0.0297, power: 0.1414
Test Batch 19/157, reward: 10.064, revenue_rate: 0.2609, distance: 4.2141, memory: 0.0397, power: 0.1280
Test Batch 20/157, reward: 11.252, revenue_rate: 0.2944, distance: 4.7628, memory: 0.0410, power: 0.1462
Test Batch 21/157, reward: 11.269, revenue_rate: 0.2888, distance: 4.5407, memory: 0.0335, power: 0.1406
Test Batch 22/157, reward: 12.399, revenue_rate: 0.3245, distance: 5.2519, memory: 0.0432, power: 0.1562
Test Batch 23/157, reward: 10.760, revenue_rate: 0.2823, distance: 4.6471, memory: 0.0610, power: 0.1368
Test Batch 24/157, reward: 11.170, revenue_rate: 0.2904, distance: 4.6517, memory: 0.0278, power: 0.1400
Test Batch 25/157, reward: 10.572, revenue_rate: 0.2699, distance: 4.2645, memory: 0.0140, power: 0.1308
Test Batch 26/157, reward: 13.525, revenue_rate: 0.3472, distance: 5.4994, memory: 0.0512, power: 0.1721
Test Batch 27/157, reward: 11.211, revenue_rate: 0.2896, distance: 4.5595, memory: 0.0255, power: 0.1407
Test Batch 28/157, reward: 10.655, revenue_rate: 0.2756, distance: 4.4802, memory: 0.0461, power: 0.1363
Test Batch 29/157, reward: 10.415, revenue_rate: 0.2686, distance: 4.3326, memory: 0.0474, power: 0.1330
Test Batch 30/157, reward: 10.021, revenue_rate: 0.2632, distance: 4.3482, memory: 0.0368, power: 0.1325
Test Batch 31/157, reward: 10.695, revenue_rate: 0.2737, distance: 4.2840, memory: 0.0265, power: 0.1285
Test Batch 32/157, reward: 11.218, revenue_rate: 0.2882, distance: 4.5462, memory: 0.0583, power: 0.1408
Test Batch 33/157, reward: 11.185, revenue_rate: 0.2883, distance: 4.6858, memory: 0.0432, power: 0.1411
Test Batch 34/157, reward: 10.238, revenue_rate: 0.2636, distance: 4.2747, memory: 0.0377, power: 0.1293
Test Batch 35/157, reward: 10.759, revenue_rate: 0.2792, distance: 4.6736, memory: 0.0731, power: 0.1389
Test Batch 36/157, reward: 11.438, revenue_rate: 0.2957, distance: 4.8677, memory: 0.0676, power: 0.1478
Test Batch 37/157, reward: 11.673, revenue_rate: 0.2959, distance: 4.6598, memory: 0.0300, power: 0.1454
Test Batch 38/157, reward: 11.442, revenue_rate: 0.2975, distance: 4.8758, memory: 0.0053, power: 0.1467
Test Batch 39/157, reward: 11.851, revenue_rate: 0.3065, distance: 4.9154, memory: 0.0317, power: 0.1417
Test Batch 40/157, reward: 10.919, revenue_rate: 0.2790, distance: 4.3729, memory: 0.0166, power: 0.1315
Test Batch 41/157, reward: 10.977, revenue_rate: 0.2867, distance: 4.7126, memory: 0.0534, power: 0.1455
Test Batch 42/157, reward: 10.897, revenue_rate: 0.2818, distance: 4.6647, memory: 0.0674, power: 0.1432
Test Batch 43/157, reward: 11.345, revenue_rate: 0.2888, distance: 4.5488, memory: 0.0188, power: 0.1401
Test Batch 44/157, reward: 11.335, revenue_rate: 0.2931, distance: 4.4924, memory: 0.0236, power: 0.1391
Test Batch 45/157, reward: 10.817, revenue_rate: 0.2790, distance: 4.3673, memory: 0.0592, power: 0.1431
Test Batch 46/157, reward: 10.835, revenue_rate: 0.2805, distance: 4.4667, memory: 0.0170, power: 0.1333
Test Batch 47/157, reward: 12.397, revenue_rate: 0.3201, distance: 5.0614, memory: 0.0273, power: 0.1574
Test Batch 48/157, reward: 11.338, revenue_rate: 0.2904, distance: 4.5088, memory: 0.0206, power: 0.1413
Test Batch 49/157, reward: 11.671, revenue_rate: 0.3018, distance: 4.9433, memory: 0.0436, power: 0.1529
Test Batch 50/157, reward: 11.055, revenue_rate: 0.2809, distance: 4.6023, memory: 0.0314, power: 0.1382
Test Batch 51/157, reward: 10.951, revenue_rate: 0.2848, distance: 4.6114, memory: 0.0417, power: 0.1415
Test Batch 52/157, reward: 10.468, revenue_rate: 0.2726, distance: 4.4572, memory: 0.0760, power: 0.1374
Test Batch 53/157, reward: 11.043, revenue_rate: 0.2847, distance: 4.5676, memory: 0.0193, power: 0.1370
Test Batch 54/157, reward: 10.794, revenue_rate: 0.2801, distance: 4.5885, memory: 0.0357, power: 0.1353
Test Batch 55/157, reward: 11.063, revenue_rate: 0.2880, distance: 4.7255, memory: 0.0206, power: 0.1379
Test Batch 56/157, reward: 10.797, revenue_rate: 0.2763, distance: 4.5791, memory: 0.0844, power: 0.1369
Test Batch 57/157, reward: 11.408, revenue_rate: 0.2991, distance: 4.9253, memory: 0.0744, power: 0.1477
Test Batch 58/157, reward: 11.610, revenue_rate: 0.3042, distance: 4.8210, memory: 0.0685, power: 0.1531
Test Batch 59/157, reward: 10.892, revenue_rate: 0.2814, distance: 4.5762, memory: 0.0299, power: 0.1438
Test Batch 60/157, reward: 11.691, revenue_rate: 0.3036, distance: 4.7688, memory: 0.0755, power: 0.1490
Test Batch 61/157, reward: 9.872, revenue_rate: 0.2544, distance: 4.1258, memory: 0.0424, power: 0.1232
Test Batch 62/157, reward: 10.791, revenue_rate: 0.2778, distance: 4.5518, memory: 0.0597, power: 0.1413
Test Batch 63/157, reward: 11.704, revenue_rate: 0.3029, distance: 4.8516, memory: 0.0444, power: 0.1494
Test Batch 64/157, reward: 11.448, revenue_rate: 0.2958, distance: 4.7609, memory: 0.0453, power: 0.1445
Test Batch 65/157, reward: 12.327, revenue_rate: 0.3198, distance: 5.1942, memory: 0.0426, power: 0.1499
Test Batch 66/157, reward: 11.330, revenue_rate: 0.2933, distance: 4.7179, memory: 0.0323, power: 0.1422
Test Batch 67/157, reward: 11.881, revenue_rate: 0.3067, distance: 4.9835, memory: 0.0292, power: 0.1471
Test Batch 68/157, reward: 10.816, revenue_rate: 0.2856, distance: 4.8407, memory: 0.0517, power: 0.1413
Test Batch 69/157, reward: 12.190, revenue_rate: 0.3187, distance: 5.2164, memory: 0.0436, power: 0.1535
Test Batch 70/157, reward: 11.904, revenue_rate: 0.3098, distance: 5.0640, memory: 0.0578, power: 0.1538
Test Batch 71/157, reward: 12.216, revenue_rate: 0.3178, distance: 5.0927, memory: 0.0132, power: 0.1556
Test Batch 72/157, reward: 10.821, revenue_rate: 0.2800, distance: 4.5555, memory: 0.0384, power: 0.1392
Test Batch 73/157, reward: 11.881, revenue_rate: 0.3046, distance: 4.9718, memory: 0.0743, power: 0.1486
Test Batch 74/157, reward: 11.202, revenue_rate: 0.2883, distance: 4.7977, memory: 0.0416, power: 0.1413
Test Batch 75/157, reward: 10.369, revenue_rate: 0.2727, distance: 4.4964, memory: 0.0472, power: 0.1337
Test Batch 76/157, reward: 11.087, revenue_rate: 0.2889, distance: 4.7804, memory: 0.0707, power: 0.1435
Test Batch 77/157, reward: 11.507, revenue_rate: 0.3004, distance: 4.6535, memory: 0.0708, power: 0.1501
Test Batch 78/157, reward: 10.961, revenue_rate: 0.2835, distance: 4.6651, memory: 0.0468, power: 0.1405
Test Batch 79/157, reward: 11.946, revenue_rate: 0.3077, distance: 4.9129, memory: 0.0320, power: 0.1499
Test Batch 80/157, reward: 11.100, revenue_rate: 0.2896, distance: 4.7820, memory: 0.0681, power: 0.1439
Test Batch 81/157, reward: 11.686, revenue_rate: 0.3041, distance: 4.7993, memory: 0.0416, power: 0.1521
Test Batch 82/157, reward: 10.388, revenue_rate: 0.2752, distance: 4.7344, memory: 0.0802, power: 0.1409
Test Batch 83/157, reward: 10.569, revenue_rate: 0.2691, distance: 4.2700, memory: 0.0221, power: 0.1332
Test Batch 84/157, reward: 11.252, revenue_rate: 0.2977, distance: 5.1466, memory: 0.0730, power: 0.1479
Test Batch 85/157, reward: 11.180, revenue_rate: 0.2865, distance: 4.4764, memory: 0.0199, power: 0.1409
Test Batch 86/157, reward: 11.398, revenue_rate: 0.2946, distance: 4.6335, memory: 0.0549, power: 0.1441
Test Batch 87/157, reward: 11.500, revenue_rate: 0.2957, distance: 4.5424, memory: 0.0375, power: 0.1450
Test Batch 88/157, reward: 10.398, revenue_rate: 0.2686, distance: 4.2572, memory: 0.0055, power: 0.1293
Test Batch 89/157, reward: 10.233, revenue_rate: 0.2636, distance: 4.3958, memory: 0.0225, power: 0.1314
Test Batch 90/157, reward: 11.068, revenue_rate: 0.2875, distance: 4.7192, memory: 0.0985, power: 0.1395
Test Batch 91/157, reward: 12.079, revenue_rate: 0.3076, distance: 4.8218, memory: 0.0463, power: 0.1493
Test Batch 92/157, reward: 12.061, revenue_rate: 0.3111, distance: 5.0264, memory: 0.0557, power: 0.1549
Test Batch 93/157, reward: 11.044, revenue_rate: 0.2856, distance: 4.5812, memory: 0.0817, power: 0.1456
Test Batch 94/157, reward: 10.556, revenue_rate: 0.2743, distance: 4.4832, memory: 0.0366, power: 0.1348
Test Batch 95/157, reward: 11.620, revenue_rate: 0.3033, distance: 5.0007, memory: 0.0458, power: 0.1535
Test Batch 96/157, reward: 10.694, revenue_rate: 0.2773, distance: 4.4122, memory: 0.0175, power: 0.1353
Test Batch 97/157, reward: 10.170, revenue_rate: 0.2614, distance: 4.1721, memory: 0.0388, power: 0.1268
Test Batch 98/157, reward: 12.072, revenue_rate: 0.3140, distance: 5.2231, memory: 0.0503, power: 0.1566
Test Batch 99/157, reward: 12.537, revenue_rate: 0.3224, distance: 4.9640, memory: 0.0388, power: 0.1537
Test Batch 100/157, reward: 11.807, revenue_rate: 0.3022, distance: 4.8819, memory: 0.0420, power: 0.1496
Test Batch 101/157, reward: 11.105, revenue_rate: 0.2888, distance: 4.7921, memory: 0.0700, power: 0.1476
Test Batch 102/157, reward: 10.791, revenue_rate: 0.2845, distance: 4.7056, memory: 0.0439, power: 0.1411
Test Batch 103/157, reward: 12.425, revenue_rate: 0.3249, distance: 5.4972, memory: 0.0737, power: 0.1639
Test Batch 104/157, reward: 11.199, revenue_rate: 0.2894, distance: 4.6991, memory: 0.0519, power: 0.1448
Test Batch 105/157, reward: 10.057, revenue_rate: 0.2597, distance: 4.2323, memory: 0.0497, power: 0.1287
Test Batch 106/157, reward: 12.363, revenue_rate: 0.3187, distance: 5.0361, memory: 0.0250, power: 0.1536
Test Batch 107/157, reward: 11.599, revenue_rate: 0.2992, distance: 4.5278, memory: 0.0168, power: 0.1395
Test Batch 108/157, reward: 11.374, revenue_rate: 0.2971, distance: 4.9733, memory: 0.0712, power: 0.1504
Test Batch 109/157, reward: 11.385, revenue_rate: 0.2940, distance: 4.6767, memory: 0.0643, power: 0.1481
Test Batch 110/157, reward: 10.573, revenue_rate: 0.2728, distance: 4.3880, memory: 0.0391, power: 0.1363
Test Batch 111/157, reward: 10.758, revenue_rate: 0.2796, distance: 4.6972, memory: 0.0409, power: 0.1387
Test Batch 112/157, reward: 10.909, revenue_rate: 0.2793, distance: 4.4278, memory: 0.0028, power: 0.1358
Test Batch 113/157, reward: 11.235, revenue_rate: 0.2882, distance: 4.6636, memory: 0.0491, power: 0.1400
Test Batch 114/157, reward: 10.728, revenue_rate: 0.2785, distance: 4.4151, memory: 0.0237, power: 0.1327
Test Batch 115/157, reward: 11.858, revenue_rate: 0.3096, distance: 5.1047, memory: 0.0558, power: 0.1552
Test Batch 116/157, reward: 10.785, revenue_rate: 0.2814, distance: 4.5284, memory: 0.0620, power: 0.1397
Test Batch 117/157, reward: 10.260, revenue_rate: 0.2667, distance: 4.1796, memory: 0.0164, power: 0.1243
Test Batch 118/157, reward: 11.049, revenue_rate: 0.2900, distance: 4.7804, memory: 0.0605, power: 0.1451
Test Batch 119/157, reward: 11.529, revenue_rate: 0.2972, distance: 4.7711, memory: 0.0166, power: 0.1447
Test Batch 120/157, reward: 10.963, revenue_rate: 0.2828, distance: 4.5678, memory: 0.0558, power: 0.1409
Test Batch 121/157, reward: 12.201, revenue_rate: 0.3127, distance: 5.0127, memory: 0.0515, power: 0.1550
Test Batch 122/157, reward: 11.395, revenue_rate: 0.2941, distance: 4.8244, memory: 0.0534, power: 0.1479
Test Batch 123/157, reward: 11.286, revenue_rate: 0.2908, distance: 4.6371, memory: 0.0133, power: 0.1437
Test Batch 124/157, reward: 9.914, revenue_rate: 0.2586, distance: 4.3589, memory: 0.0472, power: 0.1277
Test Batch 125/157, reward: 11.185, revenue_rate: 0.2916, distance: 4.6808, memory: 0.0491, power: 0.1440
Test Batch 126/157, reward: 11.704, revenue_rate: 0.3066, distance: 4.9651, memory: 0.0321, power: 0.1486
Test Batch 127/157, reward: 10.665, revenue_rate: 0.2768, distance: 4.5856, memory: 0.0194, power: 0.1375
Test Batch 128/157, reward: 12.139, revenue_rate: 0.3106, distance: 4.9258, memory: 0.0786, power: 0.1549
Test Batch 129/157, reward: 11.236, revenue_rate: 0.2892, distance: 4.7715, memory: 0.0463, power: 0.1393
Test Batch 130/157, reward: 10.796, revenue_rate: 0.2786, distance: 4.7066, memory: 0.0533, power: 0.1427
Test Batch 131/157, reward: 11.331, revenue_rate: 0.2895, distance: 4.4658, memory: 0.0356, power: 0.1394
Test Batch 132/157, reward: 12.312, revenue_rate: 0.3212, distance: 5.2087, memory: 0.0085, power: 0.1581
Test Batch 133/157, reward: 11.463, revenue_rate: 0.3021, distance: 4.9268, memory: 0.0434, power: 0.1476
Test Batch 134/157, reward: 10.520, revenue_rate: 0.2718, distance: 4.4815, memory: 0.0545, power: 0.1359
Test Batch 135/157, reward: 9.594, revenue_rate: 0.2495, distance: 4.0677, memory: 0.0578, power: 0.1269
Test Batch 136/157, reward: 11.199, revenue_rate: 0.2906, distance: 4.7852, memory: 0.0456, power: 0.1463
Test Batch 137/157, reward: 10.694, revenue_rate: 0.2774, distance: 4.4963, memory: 0.0702, power: 0.1355
Test Batch 138/157, reward: 12.039, revenue_rate: 0.3115, distance: 5.0500, memory: 0.0326, power: 0.1486
Test Batch 139/157, reward: 11.081, revenue_rate: 0.2832, distance: 4.6791, memory: 0.0296, power: 0.1412
Test Batch 140/157, reward: 11.889, revenue_rate: 0.3065, distance: 4.8446, memory: 0.0418, power: 0.1480
Test Batch 141/157, reward: 11.945, revenue_rate: 0.3141, distance: 5.1297, memory: 0.0730, power: 0.1582
Test Batch 142/157, reward: 10.920, revenue_rate: 0.2855, distance: 4.7365, memory: 0.0692, power: 0.1459
Test Batch 143/157, reward: 10.085, revenue_rate: 0.2628, distance: 4.2698, memory: 0.0367, power: 0.1304
Test Batch 144/157, reward: 12.204, revenue_rate: 0.3182, distance: 5.2815, memory: 0.0459, power: 0.1527
Test Batch 145/157, reward: 10.903, revenue_rate: 0.2819, distance: 4.5692, memory: 0.0593, power: 0.1405
Test Batch 146/157, reward: 11.764, revenue_rate: 0.3052, distance: 4.9250, memory: 0.0499, power: 0.1440
Test Batch 147/157, reward: 12.035, revenue_rate: 0.3134, distance: 5.1426, memory: 0.0673, power: 0.1538
Test Batch 148/157, reward: 11.551, revenue_rate: 0.2936, distance: 4.6790, memory: 0.0371, power: 0.1448
Test Batch 149/157, reward: 12.329, revenue_rate: 0.3161, distance: 5.0753, memory: 0.0546, power: 0.1589
Test Batch 150/157, reward: 12.289, revenue_rate: 0.3180, distance: 5.1541, memory: 0.0350, power: 0.1572
Test Batch 151/157, reward: 11.291, revenue_rate: 0.2937, distance: 4.7568, memory: 0.0220, power: 0.1433
Test Batch 152/157, reward: 12.083, revenue_rate: 0.3121, distance: 4.9130, memory: 0.0327, power: 0.1467
Test Batch 153/157, reward: 11.020, revenue_rate: 0.2852, distance: 4.5031, memory: 0.0501, power: 0.1388
Test Batch 154/157, reward: 10.986, revenue_rate: 0.2845, distance: 4.6156, memory: 0.0486, power: 0.1390
Test Batch 155/157, reward: 11.110, revenue_rate: 0.2870, distance: 4.5671, memory: 0.0195, power: 0.1399
Test Batch 156/157, reward: 10.807, revenue_rate: 0.2811, distance: 4.6102, memory: 0.0443, power: 0.1450
Test Summary - Avg reward: 11.248, revenue_rate: 0.2911, distance: 4.7037, memory: 0.0438, power: 0.1431
验证完成 - Epoch 2, reward: 11.248, revenue_rate: 0.2911, distance: 4.7037, memory: 0.0438, power: 0.1431

开始训练 Epoch 3/3
Epoch 3, Batch 10/1563, loss: 2.915, reward: 11.368, critic_reward: 11.354, revenue_rate: 0.2943, distance: 4.7280, memory: 0.0406, power: 0.1445, lr: 0.000100, took: 127.665s
Epoch 3, Batch 20/1563, loss: 3.546, reward: 11.367, critic_reward: 11.488, revenue_rate: 0.2940, distance: 4.7537, memory: 0.0329, power: 0.1446, lr: 0.000100, took: 123.119s
Epoch 3, Batch 30/1563, loss: 3.486, reward: 11.361, critic_reward: 12.134, revenue_rate: 0.2959, distance: 4.8303, memory: 0.0519, power: 0.1454, lr: 0.000100, took: 125.343s
Epoch 3, Batch 40/1563, loss: 6.635, reward: 11.465, critic_reward: 10.217, revenue_rate: 0.2961, distance: 4.7232, memory: 0.0297, power: 0.1445, lr: 0.000100, took: 124.231s
Epoch 3, Batch 50/1563, loss: 7.202, reward: 11.335, critic_reward: 12.780, revenue_rate: 0.2934, distance: 4.7243, memory: 0.0417, power: 0.1435, lr: 0.000100, took: 122.271s
Epoch 3, Batch 60/1563, loss: 3.469, reward: 11.116, critic_reward: 11.386, revenue_rate: 0.2875, distance: 4.5968, memory: 0.0455, power: 0.1397, lr: 0.000100, took: 126.429s
Epoch 3, Batch 70/1563, loss: 4.619, reward: 11.402, critic_reward: 10.749, revenue_rate: 0.2957, distance: 4.8469, memory: 0.0406, power: 0.1463, lr: 0.000100, took: 125.778s
Epoch 3, Batch 80/1563, loss: 3.780, reward: 11.147, critic_reward: 10.701, revenue_rate: 0.2887, distance: 4.6757, memory: 0.0525, power: 0.1427, lr: 0.000100, took: 123.924s
Epoch 3, Batch 90/1563, loss: 3.291, reward: 11.604, critic_reward: 11.689, revenue_rate: 0.3014, distance: 4.9242, memory: 0.0515, power: 0.1482, lr: 0.000100, took: 128.398s
Epoch 3, Batch 100/1563, loss: 8.304, reward: 13.025, critic_reward: 11.450, revenue_rate: 0.3385, distance: 5.5523, memory: 0.0864, power: 0.1708, lr: 0.000100, took: 147.334s
Epoch 3, Batch 110/1563, loss: 5.005, reward: 15.271, critic_reward: 15.929, revenue_rate: 0.3990, distance: 6.6670, memory: 0.1155, power: 0.2022, lr: 0.000100, took: 189.674s
Epoch 3, Batch 120/1563, loss: 4.961, reward: 16.207, critic_reward: 15.893, revenue_rate: 0.4233, distance: 7.0961, memory: 0.1235, power: 0.2133, lr: 0.000100, took: 199.353s
Epoch 3, Batch 130/1563, loss: 4.471, reward: 15.441, critic_reward: 15.556, revenue_rate: 0.4010, distance: 6.6534, memory: 0.1155, power: 0.1999, lr: 0.000100, took: 182.256s
Epoch 3, Batch 140/1563, loss: 5.001, reward: 15.280, critic_reward: 14.829, revenue_rate: 0.3988, distance: 6.5454, memory: 0.0920, power: 0.2002, lr: 0.000100, took: 178.897s
Epoch 3, Batch 150/1563, loss: 5.099, reward: 14.391, critic_reward: 15.172, revenue_rate: 0.3749, distance: 6.1907, memory: 0.0899, power: 0.1880, lr: 0.000100, took: 167.621s
Epoch 3, Batch 160/1563, loss: 4.938, reward: 14.814, critic_reward: 15.140, revenue_rate: 0.3835, distance: 6.2475, memory: 0.0776, power: 0.1915, lr: 0.000100, took: 169.490s
Epoch 3, Batch 170/1563, loss: 5.802, reward: 14.326, critic_reward: 13.890, revenue_rate: 0.3732, distance: 6.1795, memory: 0.0750, power: 0.1865, lr: 0.000100, took: 175.026s
Epoch 3, Batch 180/1563, loss: 4.979, reward: 13.598, critic_reward: 13.994, revenue_rate: 0.3534, distance: 5.7493, memory: 0.0608, power: 0.1738, lr: 0.000100, took: 151.325s
Epoch 3, Batch 190/1563, loss: 3.809, reward: 13.737, critic_reward: 13.399, revenue_rate: 0.3558, distance: 5.8234, memory: 0.0790, power: 0.1770, lr: 0.000100, took: 153.883s
Epoch 3, Batch 200/1563, loss: 4.006, reward: 13.469, critic_reward: 13.683, revenue_rate: 0.3508, distance: 5.7913, memory: 0.0689, power: 0.1757, lr: 0.000100, took: 152.017s
Epoch 3, Batch 210/1563, loss: 4.973, reward: 13.651, critic_reward: 13.347, revenue_rate: 0.3541, distance: 5.7620, memory: 0.0697, power: 0.1742, lr: 0.000100, took: 150.143s
Epoch 3, Batch 220/1563, loss: 5.531, reward: 13.517, critic_reward: 13.007, revenue_rate: 0.3515, distance: 5.7749, memory: 0.0730, power: 0.1740, lr: 0.000100, took: 152.114s
Epoch 3, Batch 230/1563, loss: 5.608, reward: 13.413, critic_reward: 13.871, revenue_rate: 0.3495, distance: 5.7618, memory: 0.0627, power: 0.1730, lr: 0.000100, took: 153.269s
Epoch 3, Batch 240/1563, loss: 5.095, reward: 14.068, critic_reward: 13.559, revenue_rate: 0.3672, distance: 6.0382, memory: 0.0756, power: 0.1827, lr: 0.000100, took: 163.512s
Epoch 3, Batch 250/1563, loss: 6.934, reward: 15.067, critic_reward: 16.266, revenue_rate: 0.3897, distance: 6.4080, memory: 0.0855, power: 0.1935, lr: 0.000100, took: 169.922s
Epoch 3, Batch 260/1563, loss: 5.854, reward: 15.211, critic_reward: 14.332, revenue_rate: 0.3938, distance: 6.4563, memory: 0.0946, power: 0.1959, lr: 0.000100, took: 176.552s
Epoch 3, Batch 270/1563, loss: 7.082, reward: 15.149, critic_reward: 16.152, revenue_rate: 0.3937, distance: 6.4894, memory: 0.0912, power: 0.1962, lr: 0.000100, took: 168.089s
Epoch 3, Batch 280/1563, loss: 5.623, reward: 14.532, critic_reward: 13.825, revenue_rate: 0.3803, distance: 6.3791, memory: 0.0879, power: 0.1905, lr: 0.000100, took: 166.376s
Epoch 3, Batch 290/1563, loss: 5.683, reward: 14.212, critic_reward: 14.947, revenue_rate: 0.3699, distance: 6.0884, memory: 0.0856, power: 0.1830, lr: 0.000100, took: 161.583s
Epoch 3, Batch 300/1563, loss: 4.621, reward: 14.229, critic_reward: 13.798, revenue_rate: 0.3679, distance: 6.0384, memory: 0.0702, power: 0.1831, lr: 0.000100, took: 154.289s
Epoch 3, Batch 310/1563, loss: 5.141, reward: 13.781, critic_reward: 13.383, revenue_rate: 0.3583, distance: 5.8629, memory: 0.0787, power: 0.1790, lr: 0.000100, took: 152.336s
Epoch 3, Batch 320/1563, loss: 4.815, reward: 13.859, critic_reward: 13.819, revenue_rate: 0.3598, distance: 5.8532, memory: 0.0723, power: 0.1773, lr: 0.000100, took: 156.779s
Epoch 3, Batch 330/1563, loss: 4.342, reward: 13.315, critic_reward: 13.092, revenue_rate: 0.3464, distance: 5.6878, memory: 0.0738, power: 0.1712, lr: 0.000100, took: 147.644s
Epoch 3, Batch 340/1563, loss: 5.416, reward: 13.133, critic_reward: 12.326, revenue_rate: 0.3406, distance: 5.5719, memory: 0.0692, power: 0.1701, lr: 0.000100, took: 148.626s
Epoch 3, Batch 350/1563, loss: 6.096, reward: 13.457, critic_reward: 14.241, revenue_rate: 0.3489, distance: 5.6599, memory: 0.0740, power: 0.1721, lr: 0.000100, took: 144.185s
Epoch 3, Batch 360/1563, loss: 4.329, reward: 13.342, critic_reward: 12.970, revenue_rate: 0.3471, distance: 5.6753, memory: 0.0777, power: 0.1734, lr: 0.000100, took: 147.078s
Epoch 3, Batch 370/1563, loss: 4.759, reward: 14.121, critic_reward: 13.580, revenue_rate: 0.3672, distance: 5.9895, memory: 0.0726, power: 0.1817, lr: 0.000100, took: 155.459s
Epoch 3, Batch 380/1563, loss: 5.170, reward: 14.168, critic_reward: 13.853, revenue_rate: 0.3675, distance: 6.0712, memory: 0.0821, power: 0.1808, lr: 0.000100, took: 158.535s
Epoch 3, Batch 390/1563, loss: 4.308, reward: 14.799, critic_reward: 14.983, revenue_rate: 0.3837, distance: 6.3246, memory: 0.0934, power: 0.1924, lr: 0.000100, took: 168.346s
Epoch 3, Batch 400/1563, loss: 3.812, reward: 15.006, critic_reward: 14.635, revenue_rate: 0.3894, distance: 6.4626, memory: 0.1028, power: 0.1961, lr: 0.000100, took: 171.980s
Epoch 3, Batch 410/1563, loss: 5.558, reward: 14.721, critic_reward: 15.345, revenue_rate: 0.3845, distance: 6.4068, memory: 0.1118, power: 0.1933, lr: 0.000100, took: 164.119s
Epoch 3, Batch 420/1563, loss: 6.161, reward: 14.406, critic_reward: 13.355, revenue_rate: 0.3762, distance: 6.2809, memory: 0.1030, power: 0.1895, lr: 0.000100, took: 172.011s
Epoch 3, Batch 430/1563, loss: 4.395, reward: 14.698, critic_reward: 14.593, revenue_rate: 0.3811, distance: 6.2686, memory: 0.0869, power: 0.1910, lr: 0.000100, took: 168.247s
Epoch 3, Batch 440/1563, loss: 3.631, reward: 13.342, critic_reward: 13.511, revenue_rate: 0.3470, distance: 5.7265, memory: 0.0823, power: 0.1727, lr: 0.000100, took: 148.267s
Epoch 3, Batch 450/1563, loss: 5.039, reward: 12.185, critic_reward: 11.344, revenue_rate: 0.3163, distance: 5.1413, memory: 0.0554, power: 0.1567, lr: 0.000100, took: 132.032s
Epoch 3, Batch 460/1563, loss: 4.322, reward: 12.110, critic_reward: 12.844, revenue_rate: 0.3135, distance: 5.1107, memory: 0.0558, power: 0.1547, lr: 0.000100, took: 132.212s
Epoch 3, Batch 470/1563, loss: 3.585, reward: 12.764, critic_reward: 12.767, revenue_rate: 0.3312, distance: 5.3979, memory: 0.0701, power: 0.1648, lr: 0.000100, took: 147.023s
Epoch 3, Batch 480/1563, loss: 3.688, reward: 14.454, critic_reward: 14.467, revenue_rate: 0.3738, distance: 6.1033, memory: 0.0974, power: 0.1857, lr: 0.000100, took: 160.564s
Epoch 3, Batch 490/1563, loss: 5.950, reward: 14.915, critic_reward: 13.599, revenue_rate: 0.3896, distance: 6.5089, memory: 0.1181, power: 0.1977, lr: 0.000100, took: 183.348s
Epoch 3, Batch 500/1563, loss: 7.596, reward: 15.253, critic_reward: 17.023, revenue_rate: 0.3986, distance: 6.6213, memory: 0.1118, power: 0.2010, lr: 0.000100, took: 175.258s
Epoch 3, Batch 510/1563, loss: 4.234, reward: 13.348, critic_reward: 12.907, revenue_rate: 0.3447, distance: 5.6451, memory: 0.0761, power: 0.1699, lr: 0.000100, took: 146.690s
Epoch 3, Batch 520/1563, loss: 4.404, reward: 11.358, critic_reward: 11.962, revenue_rate: 0.2939, distance: 4.7854, memory: 0.0500, power: 0.1464, lr: 0.000100, took: 125.957s
Epoch 3, Batch 530/1563, loss: 4.529, reward: 11.088, critic_reward: 11.139, revenue_rate: 0.2859, distance: 4.5952, memory: 0.0436, power: 0.1401, lr: 0.000100, took: 118.757s
Epoch 3, Batch 540/1563, loss: 3.271, reward: 11.363, critic_reward: 11.611, revenue_rate: 0.2941, distance: 4.7616, memory: 0.0432, power: 0.1427, lr: 0.000100, took: 125.153s
Epoch 3, Batch 550/1563, loss: 4.375, reward: 10.740, critic_reward: 10.650, revenue_rate: 0.2762, distance: 4.3911, memory: 0.0375, power: 0.1348, lr: 0.000100, took: 119.817s
Epoch 3, Batch 560/1563, loss: 6.254, reward: 11.149, critic_reward: 12.248, revenue_rate: 0.2878, distance: 4.6244, memory: 0.0434, power: 0.1411, lr: 0.000100, took: 123.941s
Epoch 3, Batch 570/1563, loss: 4.075, reward: 11.349, critic_reward: 10.752, revenue_rate: 0.2944, distance: 4.7767, memory: 0.0478, power: 0.1453, lr: 0.000100, took: 125.930s
Epoch 3, Batch 580/1563, loss: 4.107, reward: 11.118, critic_reward: 11.157, revenue_rate: 0.2892, distance: 4.6775, memory: 0.0319, power: 0.1413, lr: 0.000100, took: 124.005s
Epoch 3, Batch 590/1563, loss: 3.450, reward: 11.122, critic_reward: 10.603, revenue_rate: 0.2883, distance: 4.6896, memory: 0.0527, power: 0.1421, lr: 0.000100, took: 125.573s
Epoch 3, Batch 600/1563, loss: 5.434, reward: 10.966, critic_reward: 12.420, revenue_rate: 0.2848, distance: 4.6120, memory: 0.0504, power: 0.1417, lr: 0.000100, took: 122.288s
Epoch 3, Batch 610/1563, loss: 5.877, reward: 11.259, critic_reward: 10.053, revenue_rate: 0.2913, distance: 4.6912, memory: 0.0542, power: 0.1438, lr: 0.000100, took: 121.035s
Epoch 3, Batch 620/1563, loss: 4.239, reward: 11.106, critic_reward: 11.871, revenue_rate: 0.2880, distance: 4.6804, memory: 0.0514, power: 0.1422, lr: 0.000100, took: 124.895s
Epoch 3, Batch 630/1563, loss: 4.925, reward: 11.360, critic_reward: 10.650, revenue_rate: 0.2947, distance: 4.7670, memory: 0.0478, power: 0.1443, lr: 0.000100, took: 124.988s
Epoch 3, Batch 640/1563, loss: 3.363, reward: 11.161, critic_reward: 11.480, revenue_rate: 0.2892, distance: 4.6616, memory: 0.0353, power: 0.1414, lr: 0.000100, took: 124.889s
Epoch 3, Batch 650/1563, loss: 3.574, reward: 11.343, critic_reward: 10.854, revenue_rate: 0.2930, distance: 4.7692, memory: 0.0374, power: 0.1437, lr: 0.000100, took: 126.683s
Epoch 3, Batch 660/1563, loss: 4.562, reward: 11.248, critic_reward: 12.065, revenue_rate: 0.2908, distance: 4.6863, memory: 0.0445, power: 0.1415, lr: 0.000100, took: 126.924s
Epoch 3, Batch 670/1563, loss: 4.870, reward: 11.388, critic_reward: 10.643, revenue_rate: 0.2940, distance: 4.6929, memory: 0.0334, power: 0.1436, lr: 0.000100, took: 123.788s
Epoch 3, Batch 680/1563, loss: 3.398, reward: 10.908, critic_reward: 10.866, revenue_rate: 0.2811, distance: 4.5046, memory: 0.0433, power: 0.1370, lr: 0.000100, took: 121.707s
Epoch 3, Batch 690/1563, loss: 7.492, reward: 11.690, critic_reward: 10.833, revenue_rate: 0.3028, distance: 4.8773, memory: 0.0445, power: 0.1482, lr: 0.000100, took: 125.763s
Epoch 3, Batch 700/1563, loss: 4.364, reward: 11.547, critic_reward: 12.214, revenue_rate: 0.2980, distance: 4.8372, memory: 0.0471, power: 0.1461, lr: 0.000100, took: 127.242s
Epoch 3, Batch 710/1563, loss: 3.916, reward: 11.456, critic_reward: 10.770, revenue_rate: 0.2950, distance: 4.7824, memory: 0.0472, power: 0.1455, lr: 0.000100, took: 123.140s
Epoch 3, Batch 720/1563, loss: 4.053, reward: 11.718, critic_reward: 11.986, revenue_rate: 0.3018, distance: 4.8552, memory: 0.0476, power: 0.1496, lr: 0.000100, took: 128.514s
Epoch 3, Batch 730/1563, loss: 3.552, reward: 11.304, critic_reward: 11.115, revenue_rate: 0.2926, distance: 4.7620, memory: 0.0487, power: 0.1442, lr: 0.000100, took: 124.781s
Epoch 3, Batch 740/1563, loss: 3.904, reward: 11.358, critic_reward: 11.575, revenue_rate: 0.2936, distance: 4.7140, memory: 0.0479, power: 0.1422, lr: 0.000100, took: 123.809s
Epoch 3, Batch 750/1563, loss: 4.216, reward: 11.510, critic_reward: 11.029, revenue_rate: 0.2968, distance: 4.7349, memory: 0.0404, power: 0.1461, lr: 0.000100, took: 124.933s
Epoch 3, Batch 760/1563, loss: 3.204, reward: 11.500, critic_reward: 11.719, revenue_rate: 0.2989, distance: 4.9249, memory: 0.0416, power: 0.1473, lr: 0.000100, took: 129.980s
Epoch 3, Batch 770/1563, loss: 4.273, reward: 11.884, critic_reward: 11.220, revenue_rate: 0.3064, distance: 4.9558, memory: 0.0460, power: 0.1509, lr: 0.000100, took: 127.895s
Epoch 3, Batch 780/1563, loss: 3.556, reward: 11.295, critic_reward: 11.465, revenue_rate: 0.2927, distance: 4.7847, memory: 0.0512, power: 0.1439, lr: 0.000100, took: 125.734s
Epoch 3, Batch 790/1563, loss: 3.925, reward: 11.917, critic_reward: 11.602, revenue_rate: 0.3078, distance: 4.9917, memory: 0.0448, power: 0.1510, lr: 0.000100, took: 128.874s
Epoch 3, Batch 800/1563, loss: 3.225, reward: 11.515, critic_reward: 11.575, revenue_rate: 0.2985, distance: 4.8672, memory: 0.0466, power: 0.1463, lr: 0.000100, took: 127.392s
Epoch 3, Batch 810/1563, loss: 4.017, reward: 11.533, critic_reward: 11.307, revenue_rate: 0.2979, distance: 4.8401, memory: 0.0418, power: 0.1463, lr: 0.000100, took: 126.337s
Epoch 3, Batch 820/1563, loss: 3.673, reward: 11.551, critic_reward: 11.317, revenue_rate: 0.2990, distance: 4.8598, memory: 0.0534, power: 0.1474, lr: 0.000100, took: 126.300s
Epoch 3, Batch 830/1563, loss: 4.160, reward: 11.578, critic_reward: 11.906, revenue_rate: 0.3005, distance: 4.8634, memory: 0.0460, power: 0.1468, lr: 0.000100, took: 126.843s
Epoch 3, Batch 840/1563, loss: 3.646, reward: 11.551, critic_reward: 11.361, revenue_rate: 0.2985, distance: 4.7713, memory: 0.0392, power: 0.1465, lr: 0.000100, took: 124.042s
Epoch 3, Batch 850/1563, loss: 3.593, reward: 11.532, critic_reward: 11.169, revenue_rate: 0.3000, distance: 4.9238, memory: 0.0513, power: 0.1474, lr: 0.000100, took: 125.986s
Epoch 3, Batch 860/1563, loss: 5.570, reward: 12.293, critic_reward: 12.683, revenue_rate: 0.3182, distance: 5.1130, memory: 0.0509, power: 0.1560, lr: 0.000100, took: 129.154s
Epoch 3, Batch 870/1563, loss: 5.555, reward: 12.141, critic_reward: 12.505, revenue_rate: 0.3136, distance: 5.1287, memory: 0.0441, power: 0.1539, lr: 0.000100, took: 132.001s
Epoch 3, Batch 880/1563, loss: 4.061, reward: 11.504, critic_reward: 10.988, revenue_rate: 0.2980, distance: 4.8785, memory: 0.0559, power: 0.1479, lr: 0.000100, took: 128.712s
Epoch 3, Batch 890/1563, loss: 3.464, reward: 11.277, critic_reward: 11.386, revenue_rate: 0.2934, distance: 4.7715, memory: 0.0552, power: 0.1449, lr: 0.000100, took: 127.564s
Epoch 3, Batch 900/1563, loss: 2.928, reward: 11.414, critic_reward: 11.175, revenue_rate: 0.2949, distance: 4.7359, memory: 0.0489, power: 0.1445, lr: 0.000100, took: 126.589s
Epoch 3, Batch 910/1563, loss: 4.611, reward: 11.182, critic_reward: 12.317, revenue_rate: 0.2899, distance: 4.7258, memory: 0.0471, power: 0.1426, lr: 0.000100, took: 124.961s
Epoch 3, Batch 920/1563, loss: 3.492, reward: 11.528, critic_reward: 11.117, revenue_rate: 0.2981, distance: 4.8309, memory: 0.0512, power: 0.1466, lr: 0.000100, took: 124.703s
Epoch 3, Batch 930/1563, loss: 3.745, reward: 11.520, critic_reward: 11.652, revenue_rate: 0.2974, distance: 4.7744, memory: 0.0460, power: 0.1456, lr: 0.000100, took: 129.629s
Epoch 3, Batch 940/1563, loss: 3.347, reward: 12.089, critic_reward: 12.017, revenue_rate: 0.3144, distance: 5.1760, memory: 0.0657, power: 0.1566, lr: 0.000100, took: 130.435s
Epoch 3, Batch 950/1563, loss: 2.896, reward: 11.711, critic_reward: 11.714, revenue_rate: 0.3028, distance: 4.9254, memory: 0.0629, power: 0.1503, lr: 0.000100, took: 131.859s
Epoch 3, Batch 960/1563, loss: 5.197, reward: 12.222, critic_reward: 11.569, revenue_rate: 0.3161, distance: 5.1893, memory: 0.0624, power: 0.1563, lr: 0.000100, took: 129.655s
Epoch 3, Batch 970/1563, loss: 4.806, reward: 12.012, critic_reward: 11.884, revenue_rate: 0.3122, distance: 5.0789, memory: 0.0447, power: 0.1534, lr: 0.000100, took: 128.903s
Epoch 3, Batch 980/1563, loss: 3.146, reward: 11.655, critic_reward: 11.194, revenue_rate: 0.3016, distance: 4.9610, memory: 0.0545, power: 0.1488, lr: 0.000100, took: 128.996s
Epoch 3, Batch 990/1563, loss: 4.227, reward: 11.809, critic_reward: 11.750, revenue_rate: 0.3058, distance: 4.9755, memory: 0.0489, power: 0.1495, lr: 0.000100, took: 129.875s
Epoch 3, Batch 1000/1563, loss: 3.352, reward: 11.631, critic_reward: 11.467, revenue_rate: 0.3021, distance: 4.9754, memory: 0.0606, power: 0.1497, lr: 0.000100, took: 127.967s
Epoch 3, Batch 1010/1563, loss: 4.574, reward: 11.570, critic_reward: 12.334, revenue_rate: 0.2991, distance: 4.8194, memory: 0.0482, power: 0.1462, lr: 0.000100, took: 128.006s
Epoch 3, Batch 1020/1563, loss: 3.392, reward: 11.407, critic_reward: 11.150, revenue_rate: 0.2951, distance: 4.8112, memory: 0.0562, power: 0.1458, lr: 0.000100, took: 126.952s
Epoch 3, Batch 1030/1563, loss: 3.145, reward: 11.292, critic_reward: 11.318, revenue_rate: 0.2925, distance: 4.8169, memory: 0.0543, power: 0.1440, lr: 0.000100, took: 125.476s
Epoch 3, Batch 1040/1563, loss: 4.469, reward: 11.914, critic_reward: 12.618, revenue_rate: 0.3082, distance: 5.0338, memory: 0.0505, power: 0.1527, lr: 0.000100, took: 130.247s
Epoch 3, Batch 1050/1563, loss: 3.566, reward: 11.875, critic_reward: 11.693, revenue_rate: 0.3069, distance: 4.9236, memory: 0.0460, power: 0.1519, lr: 0.000100, took: 127.367s
Epoch 3, Batch 1060/1563, loss: 3.231, reward: 11.605, critic_reward: 11.426, revenue_rate: 0.3004, distance: 4.8874, memory: 0.0597, power: 0.1471, lr: 0.000100, took: 125.753s
Epoch 3, Batch 1070/1563, loss: 2.896, reward: 11.167, critic_reward: 11.317, revenue_rate: 0.2896, distance: 4.7472, memory: 0.0539, power: 0.1426, lr: 0.000100, took: 128.821s
Epoch 3, Batch 1080/1563, loss: 4.261, reward: 11.503, critic_reward: 11.253, revenue_rate: 0.2980, distance: 4.8776, memory: 0.0563, power: 0.1480, lr: 0.000100, took: 127.512s
Epoch 3, Batch 1090/1563, loss: 4.848, reward: 11.759, critic_reward: 11.404, revenue_rate: 0.3046, distance: 4.9505, memory: 0.0508, power: 0.1512, lr: 0.000100, took: 126.736s
Epoch 3, Batch 1100/1563, loss: 3.130, reward: 11.319, critic_reward: 11.205, revenue_rate: 0.2931, distance: 4.8204, memory: 0.0619, power: 0.1459, lr: 0.000100, took: 124.594s
Epoch 3, Batch 1110/1563, loss: 4.670, reward: 11.153, critic_reward: 11.024, revenue_rate: 0.2888, distance: 4.7381, memory: 0.0625, power: 0.1441, lr: 0.000100, took: 125.109s
Epoch 3, Batch 1120/1563, loss: 4.487, reward: 11.806, critic_reward: 11.048, revenue_rate: 0.3061, distance: 5.0274, memory: 0.0549, power: 0.1503, lr: 0.000100, took: 127.535s
Epoch 3, Batch 1130/1563, loss: 3.212, reward: 11.480, critic_reward: 11.936, revenue_rate: 0.2973, distance: 4.8331, memory: 0.0587, power: 0.1474, lr: 0.000100, took: 127.807s
Epoch 3, Batch 1140/1563, loss: 3.492, reward: 11.695, critic_reward: 12.116, revenue_rate: 0.3037, distance: 4.9483, memory: 0.0667, power: 0.1504, lr: 0.000100, took: 131.992s
Epoch 3, Batch 1150/1563, loss: 6.996, reward: 11.595, critic_reward: 10.346, revenue_rate: 0.3026, distance: 4.9923, memory: 0.0738, power: 0.1521, lr: 0.000100, took: 133.494s
Epoch 3, Batch 1160/1563, loss: 5.155, reward: 11.093, critic_reward: 11.534, revenue_rate: 0.2880, distance: 4.7287, memory: 0.0657, power: 0.1436, lr: 0.000100, took: 126.170s
Epoch 3, Batch 1170/1563, loss: 4.285, reward: 10.583, critic_reward: 10.404, revenue_rate: 0.2767, distance: 4.6088, memory: 0.0643, power: 0.1386, lr: 0.000100, took: 119.960s
Epoch 3, Batch 1180/1563, loss: 3.045, reward: 10.367, critic_reward: 10.703, revenue_rate: 0.2690, distance: 4.3781, memory: 0.0660, power: 0.1331, lr: 0.000100, took: 119.356s
Epoch 3, Batch 1190/1563, loss: 3.841, reward: 10.559, critic_reward: 9.853, revenue_rate: 0.2750, distance: 4.5730, memory: 0.0822, power: 0.1378, lr: 0.000100, took: 119.963s
Epoch 3, Batch 1200/1563, loss: 3.836, reward: 10.358, critic_reward: 10.469, revenue_rate: 0.2694, distance: 4.4977, memory: 0.0839, power: 0.1379, lr: 0.000100, took: 122.535s
Epoch 3, Batch 1210/1563, loss: 4.302, reward: 11.521, critic_reward: 11.826, revenue_rate: 0.2995, distance: 4.9369, memory: 0.0833, power: 0.1508, lr: 0.000100, took: 124.506s
Epoch 3, Batch 1220/1563, loss: 2.531, reward: 10.592, critic_reward: 10.654, revenue_rate: 0.2757, distance: 4.5678, memory: 0.0741, power: 0.1395, lr: 0.000100, took: 125.418s
Epoch 3, Batch 1230/1563, loss: 3.214, reward: 10.534, critic_reward: 10.256, revenue_rate: 0.2738, distance: 4.5011, memory: 0.0770, power: 0.1365, lr: 0.000100, took: 119.365s
Epoch 3, Batch 1240/1563, loss: 3.864, reward: 10.734, critic_reward: 10.731, revenue_rate: 0.2797, distance: 4.6371, memory: 0.0788, power: 0.1400, lr: 0.000100, took: 119.538s
Epoch 3, Batch 1250/1563, loss: 3.419, reward: 10.622, critic_reward: 10.815, revenue_rate: 0.2767, distance: 4.5343, memory: 0.0659, power: 0.1382, lr: 0.000100, took: 118.994s
Epoch 3, Batch 1260/1563, loss: 4.685, reward: 10.583, critic_reward: 11.010, revenue_rate: 0.2741, distance: 4.4694, memory: 0.0603, power: 0.1365, lr: 0.000100, took: 118.740s
Epoch 3, Batch 1270/1563, loss: 2.706, reward: 10.795, critic_reward: 10.475, revenue_rate: 0.2802, distance: 4.6205, memory: 0.0572, power: 0.1407, lr: 0.000100, took: 121.964s
Epoch 3, Batch 1280/1563, loss: 2.907, reward: 11.290, critic_reward: 11.526, revenue_rate: 0.2918, distance: 4.7221, memory: 0.0409, power: 0.1418, lr: 0.000100, took: 121.170s
Epoch 3, Batch 1290/1563, loss: 3.600, reward: 11.468, critic_reward: 11.293, revenue_rate: 0.2966, distance: 4.8267, memory: 0.0504, power: 0.1464, lr: 0.000100, took: 121.560s
Epoch 3, Batch 1300/1563, loss: 3.519, reward: 11.429, critic_reward: 11.497, revenue_rate: 0.2949, distance: 4.6663, memory: 0.0384, power: 0.1438, lr: 0.000100, took: 126.631s
Epoch 3, Batch 1310/1563, loss: 3.335, reward: 11.144, critic_reward: 11.201, revenue_rate: 0.2894, distance: 4.7117, memory: 0.0406, power: 0.1431, lr: 0.000100, took: 125.169s
Epoch 3, Batch 1320/1563, loss: 3.766, reward: 11.578, critic_reward: 11.183, revenue_rate: 0.2982, distance: 4.7855, memory: 0.0361, power: 0.1446, lr: 0.000100, took: 125.765s
Epoch 3, Batch 1330/1563, loss: 3.720, reward: 11.661, critic_reward: 11.697, revenue_rate: 0.3001, distance: 4.8239, memory: 0.0418, power: 0.1480, lr: 0.000100, took: 130.430s
Epoch 3, Batch 1340/1563, loss: 3.012, reward: 11.640, critic_reward: 12.052, revenue_rate: 0.3005, distance: 4.8393, memory: 0.0429, power: 0.1476, lr: 0.000100, took: 128.392s
Epoch 3, Batch 1350/1563, loss: 3.750, reward: 11.479, critic_reward: 10.861, revenue_rate: 0.2971, distance: 4.7932, memory: 0.0405, power: 0.1482, lr: 0.000100, took: 126.640s
Epoch 3, Batch 1360/1563, loss: 3.101, reward: 10.897, critic_reward: 11.112, revenue_rate: 0.2818, distance: 4.5518, memory: 0.0470, power: 0.1388, lr: 0.000100, took: 122.729s
Epoch 3, Batch 1370/1563, loss: 5.413, reward: 11.974, critic_reward: 12.526, revenue_rate: 0.3099, distance: 4.9625, memory: 0.0429, power: 0.1522, lr: 0.000100, took: 132.593s
Epoch 3, Batch 1380/1563, loss: 3.490, reward: 11.288, critic_reward: 11.143, revenue_rate: 0.2921, distance: 4.7150, memory: 0.0344, power: 0.1418, lr: 0.000100, took: 127.307s
Epoch 3, Batch 1390/1563, loss: 4.140, reward: 11.367, critic_reward: 11.398, revenue_rate: 0.2947, distance: 4.7724, memory: 0.0486, power: 0.1456, lr: 0.000100, took: 127.139s
Epoch 3, Batch 1400/1563, loss: 3.739, reward: 11.220, critic_reward: 11.139, revenue_rate: 0.2910, distance: 4.7116, memory: 0.0497, power: 0.1426, lr: 0.000100, took: 123.836s
Epoch 3, Batch 1410/1563, loss: 5.076, reward: 12.026, critic_reward: 11.631, revenue_rate: 0.3105, distance: 5.0042, memory: 0.0406, power: 0.1507, lr: 0.000100, took: 127.660s
Epoch 3, Batch 1420/1563, loss: 4.803, reward: 11.553, critic_reward: 12.307, revenue_rate: 0.2984, distance: 4.8312, memory: 0.0342, power: 0.1459, lr: 0.000100, took: 127.066s
Epoch 3, Batch 1430/1563, loss: 3.949, reward: 11.374, critic_reward: 11.076, revenue_rate: 0.2951, distance: 4.8259, memory: 0.0605, power: 0.1460, lr: 0.000100, took: 125.078s
Epoch 3, Batch 1440/1563, loss: 4.036, reward: 12.082, critic_reward: 12.677, revenue_rate: 0.3120, distance: 5.0368, memory: 0.0424, power: 0.1527, lr: 0.000100, took: 128.767s
Epoch 3, Batch 1450/1563, loss: 3.408, reward: 10.775, critic_reward: 10.670, revenue_rate: 0.2787, distance: 4.5428, memory: 0.0326, power: 0.1366, lr: 0.000100, took: 121.181s
Epoch 3, Batch 1460/1563, loss: 4.360, reward: 11.067, critic_reward: 10.322, revenue_rate: 0.2867, distance: 4.6332, memory: 0.0416, power: 0.1414, lr: 0.000100, took: 120.155s
Epoch 3, Batch 1470/1563, loss: 3.993, reward: 10.960, critic_reward: 11.615, revenue_rate: 0.2834, distance: 4.5885, memory: 0.0418, power: 0.1392, lr: 0.000100, took: 125.349s
Epoch 3, Batch 1480/1563, loss: 4.383, reward: 11.388, critic_reward: 10.472, revenue_rate: 0.2937, distance: 4.7057, memory: 0.0435, power: 0.1435, lr: 0.000100, took: 125.258s
Epoch 3, Batch 1490/1563, loss: 3.315, reward: 11.459, critic_reward: 11.486, revenue_rate: 0.2959, distance: 4.7673, memory: 0.0532, power: 0.1465, lr: 0.000100, took: 124.883s
Epoch 3, Batch 1500/1563, loss: 3.596, reward: 11.531, critic_reward: 11.014, revenue_rate: 0.2980, distance: 4.8012, memory: 0.0416, power: 0.1463, lr: 0.000100, took: 126.023s
Epoch 3, Batch 1510/1563, loss: 6.733, reward: 10.845, critic_reward: 12.454, revenue_rate: 0.2800, distance: 4.5304, memory: 0.0358, power: 0.1362, lr: 0.000100, took: 125.719s
Epoch 3, Batch 1520/1563, loss: 3.131, reward: 11.183, critic_reward: 10.738, revenue_rate: 0.2888, distance: 4.6777, memory: 0.0469, power: 0.1410, lr: 0.000100, took: 123.622s
Epoch 3, Batch 1530/1563, loss: 3.949, reward: 11.465, critic_reward: 11.851, revenue_rate: 0.2959, distance: 4.7028, memory: 0.0334, power: 0.1440, lr: 0.000100, took: 127.544s
Epoch 3, Batch 1540/1563, loss: 3.314, reward: 11.323, critic_reward: 11.128, revenue_rate: 0.2932, distance: 4.7624, memory: 0.0510, power: 0.1425, lr: 0.000100, took: 125.888s
Epoch 3, Batch 1550/1563, loss: 3.300, reward: 10.993, critic_reward: 10.836, revenue_rate: 0.2846, distance: 4.5857, memory: 0.0516, power: 0.1409, lr: 0.000100, took: 119.879s
Epoch 3, Batch 1560/1563, loss: 3.352, reward: 11.643, critic_reward: 11.457, revenue_rate: 0.3014, distance: 4.9132, memory: 0.0463, power: 0.1482, lr: 0.000100, took: 128.050s
开始验证...
Test Batch 0/157, reward: 11.575, revenue_rate: 0.2987, distance: 4.6798, memory: 0.0421, power: 0.1443
Test Batch 1/157, reward: 10.457, revenue_rate: 0.2713, distance: 4.4078, memory: 0.0590, power: 0.1354
Test Batch 2/157, reward: 10.888, revenue_rate: 0.2850, distance: 4.7002, memory: 0.0364, power: 0.1379
Test Batch 3/157, reward: 10.902, revenue_rate: 0.2853, distance: 4.6106, memory: 0.0493, power: 0.1424
Test Batch 4/157, reward: 10.647, revenue_rate: 0.2757, distance: 4.5321, memory: 0.0430, power: 0.1337
Test Batch 5/157, reward: 11.007, revenue_rate: 0.2803, distance: 4.4272, memory: 0.0445, power: 0.1403
Test Batch 6/157, reward: 10.867, revenue_rate: 0.2818, distance: 4.5124, memory: 0.0597, power: 0.1378
Test Batch 7/157, reward: 10.818, revenue_rate: 0.2827, distance: 4.6793, memory: 0.0434, power: 0.1410
Test Batch 8/157, reward: 10.272, revenue_rate: 0.2694, distance: 4.4696, memory: 0.0559, power: 0.1336
Test Batch 9/157, reward: 11.446, revenue_rate: 0.2944, distance: 4.7922, memory: 0.0473, power: 0.1444
Test Batch 10/157, reward: 10.446, revenue_rate: 0.2763, distance: 4.6136, memory: 0.0812, power: 0.1391
Test Batch 11/157, reward: 12.358, revenue_rate: 0.3179, distance: 5.2830, memory: 0.0335, power: 0.1551
Test Batch 12/157, reward: 12.694, revenue_rate: 0.3260, distance: 5.1181, memory: 0.0498, power: 0.1572
Test Batch 13/157, reward: 11.114, revenue_rate: 0.2899, distance: 4.7401, memory: 0.0754, power: 0.1417
Test Batch 14/157, reward: 10.290, revenue_rate: 0.2669, distance: 4.2552, memory: 0.0523, power: 0.1303
Test Batch 15/157, reward: 12.145, revenue_rate: 0.3140, distance: 5.1242, memory: 0.0592, power: 0.1551
Test Batch 16/157, reward: 11.059, revenue_rate: 0.2797, distance: 4.2683, memory: 0.0126, power: 0.1357
Test Batch 17/157, reward: 11.578, revenue_rate: 0.3012, distance: 4.9947, memory: 0.0542, power: 0.1509
Test Batch 18/157, reward: 11.956, revenue_rate: 0.3077, distance: 4.9243, memory: 0.0811, power: 0.1583
Test Batch 19/157, reward: 11.806, revenue_rate: 0.3063, distance: 4.9174, memory: 0.0361, power: 0.1500
Test Batch 20/157, reward: 11.120, revenue_rate: 0.2911, distance: 4.7092, memory: 0.0505, power: 0.1463
Test Batch 21/157, reward: 10.943, revenue_rate: 0.2831, distance: 4.7235, memory: 0.0321, power: 0.1369
Test Batch 22/157, reward: 11.449, revenue_rate: 0.2983, distance: 4.7241, memory: 0.0441, power: 0.1435
Test Batch 23/157, reward: 11.368, revenue_rate: 0.2960, distance: 4.7164, memory: 0.0393, power: 0.1434
Test Batch 24/157, reward: 11.433, revenue_rate: 0.2964, distance: 4.7147, memory: 0.0312, power: 0.1448
Test Batch 25/157, reward: 13.077, revenue_rate: 0.3354, distance: 5.4118, memory: 0.0600, power: 0.1643
Test Batch 26/157, reward: 10.190, revenue_rate: 0.2637, distance: 4.3044, memory: 0.0404, power: 0.1305
Test Batch 27/157, reward: 11.063, revenue_rate: 0.2887, distance: 4.7533, memory: 0.0499, power: 0.1482
Test Batch 28/157, reward: 10.966, revenue_rate: 0.2819, distance: 4.4576, memory: 0.0169, power: 0.1344
Test Batch 29/157, reward: 10.793, revenue_rate: 0.2800, distance: 4.6398, memory: 0.0873, power: 0.1391
Test Batch 30/157, reward: 10.919, revenue_rate: 0.2878, distance: 4.8305, memory: 0.0540, power: 0.1433
Test Batch 31/157, reward: 11.200, revenue_rate: 0.2876, distance: 4.5766, memory: 0.0326, power: 0.1400
Test Batch 32/157, reward: 10.620, revenue_rate: 0.2738, distance: 4.3996, memory: 0.0344, power: 0.1328
Test Batch 33/157, reward: 11.508, revenue_rate: 0.2941, distance: 4.6166, memory: 0.0362, power: 0.1438
Test Batch 34/157, reward: 11.492, revenue_rate: 0.2974, distance: 4.9476, memory: 0.0585, power: 0.1476
Test Batch 35/157, reward: 12.276, revenue_rate: 0.3147, distance: 5.0380, memory: 0.0441, power: 0.1533
Test Batch 36/157, reward: 10.268, revenue_rate: 0.2678, distance: 4.5412, memory: 0.0603, power: 0.1345
Test Batch 37/157, reward: 11.320, revenue_rate: 0.2860, distance: 4.4586, memory: 0.0294, power: 0.1405
Test Batch 38/157, reward: 12.680, revenue_rate: 0.3299, distance: 5.3371, memory: 0.0341, power: 0.1597
Test Batch 39/157, reward: 10.648, revenue_rate: 0.2785, distance: 4.6378, memory: 0.0846, power: 0.1377
Test Batch 40/157, reward: 12.466, revenue_rate: 0.3203, distance: 5.1495, memory: 0.0621, power: 0.1586
Test Batch 41/157, reward: 12.105, revenue_rate: 0.3131, distance: 4.9058, memory: 0.0488, power: 0.1541
Test Batch 42/157, reward: 11.513, revenue_rate: 0.2966, distance: 4.8505, memory: 0.0393, power: 0.1476
Test Batch 43/157, reward: 10.666, revenue_rate: 0.2717, distance: 4.2686, memory: 0.0426, power: 0.1381
Test Batch 44/157, reward: 11.352, revenue_rate: 0.2957, distance: 4.6871, memory: 0.0594, power: 0.1468
Test Batch 45/157, reward: 10.593, revenue_rate: 0.2735, distance: 4.3225, memory: 0.0423, power: 0.1364
Test Batch 46/157, reward: 11.075, revenue_rate: 0.2859, distance: 4.5272, memory: 0.0475, power: 0.1406
Test Batch 47/157, reward: 11.156, revenue_rate: 0.2893, distance: 4.6108, memory: 0.0377, power: 0.1392
Test Batch 48/157, reward: 12.224, revenue_rate: 0.3168, distance: 5.2038, memory: 0.0366, power: 0.1560
Test Batch 49/157, reward: 11.799, revenue_rate: 0.3066, distance: 5.0963, memory: 0.0725, power: 0.1573
Test Batch 50/157, reward: 11.145, revenue_rate: 0.2833, distance: 4.6328, memory: 0.0342, power: 0.1394
Test Batch 51/157, reward: 12.283, revenue_rate: 0.3192, distance: 5.1119, memory: 0.0352, power: 0.1521
Test Batch 52/157, reward: 10.138, revenue_rate: 0.2639, distance: 4.3129, memory: 0.0292, power: 0.1315
Test Batch 53/157, reward: 13.251, revenue_rate: 0.3438, distance: 5.6016, memory: 0.0576, power: 0.1661
Test Batch 54/157, reward: 10.510, revenue_rate: 0.2733, distance: 4.4829, memory: 0.0509, power: 0.1370
Test Batch 55/157, reward: 12.014, revenue_rate: 0.3097, distance: 4.8565, memory: 0.0153, power: 0.1495
Test Batch 56/157, reward: 12.779, revenue_rate: 0.3221, distance: 4.9979, memory: 0.0401, power: 0.1548
Test Batch 57/157, reward: 10.680, revenue_rate: 0.2793, distance: 4.5892, memory: 0.0300, power: 0.1368
Test Batch 58/157, reward: 12.806, revenue_rate: 0.3357, distance: 5.3686, memory: 0.0600, power: 0.1692
Test Batch 59/157, reward: 11.411, revenue_rate: 0.2943, distance: 4.7632, memory: 0.0366, power: 0.1432
Test Batch 60/157, reward: 9.872, revenue_rate: 0.2585, distance: 4.2193, memory: 0.0675, power: 0.1280
Test Batch 61/157, reward: 11.364, revenue_rate: 0.2957, distance: 4.9970, memory: 0.0845, power: 0.1492
Test Batch 62/157, reward: 11.599, revenue_rate: 0.2992, distance: 4.9505, memory: 0.0381, power: 0.1454
Test Batch 63/157, reward: 10.649, revenue_rate: 0.2757, distance: 4.4168, memory: 0.0193, power: 0.1356
Test Batch 64/157, reward: 11.504, revenue_rate: 0.2964, distance: 4.7254, memory: 0.0322, power: 0.1410
Test Batch 65/157, reward: 11.843, revenue_rate: 0.3089, distance: 5.0689, memory: 0.0756, power: 0.1534
Test Batch 66/157, reward: 10.668, revenue_rate: 0.2784, distance: 4.6458, memory: 0.0532, power: 0.1423
Test Batch 67/157, reward: 12.423, revenue_rate: 0.3210, distance: 5.1798, memory: 0.0873, power: 0.1562
Test Batch 68/157, reward: 11.085, revenue_rate: 0.2877, distance: 4.4890, memory: 0.0556, power: 0.1350
Test Batch 69/157, reward: 11.107, revenue_rate: 0.2908, distance: 4.7518, memory: 0.0564, power: 0.1459
Test Batch 70/157, reward: 10.394, revenue_rate: 0.2713, distance: 4.4769, memory: 0.0541, power: 0.1340
Test Batch 71/157, reward: 10.174, revenue_rate: 0.2653, distance: 4.2685, memory: 0.0382, power: 0.1299
Test Batch 72/157, reward: 12.779, revenue_rate: 0.3279, distance: 5.1261, memory: 0.0308, power: 0.1600
Test Batch 73/157, reward: 10.823, revenue_rate: 0.2773, distance: 4.5010, memory: 0.0670, power: 0.1333
Test Batch 74/157, reward: 10.435, revenue_rate: 0.2651, distance: 4.1683, memory: 0.0223, power: 0.1303
Test Batch 75/157, reward: 10.591, revenue_rate: 0.2777, distance: 4.5333, memory: 0.0436, power: 0.1385
Test Batch 76/157, reward: 10.950, revenue_rate: 0.2864, distance: 4.8325, memory: 0.0641, power: 0.1447
Test Batch 77/157, reward: 10.416, revenue_rate: 0.2746, distance: 4.4672, memory: 0.0451, power: 0.1345
Test Batch 78/157, reward: 11.143, revenue_rate: 0.2893, distance: 4.8129, memory: 0.0520, power: 0.1426
Test Batch 79/157, reward: 11.112, revenue_rate: 0.2887, distance: 4.7824, memory: 0.0694, power: 0.1439
Test Batch 80/157, reward: 11.502, revenue_rate: 0.2986, distance: 4.7934, memory: 0.0949, power: 0.1488
Test Batch 81/157, reward: 11.887, revenue_rate: 0.3108, distance: 4.9780, memory: 0.0772, power: 0.1543
Test Batch 82/157, reward: 10.401, revenue_rate: 0.2736, distance: 4.5740, memory: 0.0654, power: 0.1338
Test Batch 83/157, reward: 10.992, revenue_rate: 0.2824, distance: 4.6350, memory: 0.0638, power: 0.1386
Test Batch 84/157, reward: 12.397, revenue_rate: 0.3238, distance: 5.3004, memory: 0.0550, power: 0.1566
Test Batch 85/157, reward: 11.990, revenue_rate: 0.3081, distance: 4.8699, memory: 0.0108, power: 0.1465
Test Batch 86/157, reward: 10.448, revenue_rate: 0.2707, distance: 4.3391, memory: 0.0402, power: 0.1341
Test Batch 87/157, reward: 11.714, revenue_rate: 0.3031, distance: 4.7843, memory: 0.0339, power: 0.1444
Test Batch 88/157, reward: 10.853, revenue_rate: 0.2828, distance: 4.6583, memory: 0.0488, power: 0.1397
Test Batch 89/157, reward: 11.443, revenue_rate: 0.2926, distance: 4.6589, memory: 0.0410, power: 0.1416
Test Batch 90/157, reward: 11.316, revenue_rate: 0.2949, distance: 4.9542, memory: 0.0548, power: 0.1395
Test Batch 91/157, reward: 11.921, revenue_rate: 0.3049, distance: 4.9527, memory: 0.0353, power: 0.1471
Test Batch 92/157, reward: 10.351, revenue_rate: 0.2653, distance: 4.2042, memory: 0.0262, power: 0.1280
Test Batch 93/157, reward: 10.397, revenue_rate: 0.2689, distance: 4.3054, memory: 0.0726, power: 0.1347
Test Batch 94/157, reward: 11.939, revenue_rate: 0.3125, distance: 5.2462, memory: 0.0615, power: 0.1573
Test Batch 95/157, reward: 10.659, revenue_rate: 0.2760, distance: 4.4098, memory: 0.0302, power: 0.1359
Test Batch 96/157, reward: 10.323, revenue_rate: 0.2687, distance: 4.3515, memory: 0.0628, power: 0.1357
Test Batch 97/157, reward: 10.573, revenue_rate: 0.2746, distance: 4.5850, memory: 0.0702, power: 0.1398
Test Batch 98/157, reward: 11.275, revenue_rate: 0.2926, distance: 4.7921, memory: 0.0476, power: 0.1470
Test Batch 99/157, reward: 11.243, revenue_rate: 0.2926, distance: 4.7544, memory: 0.0619, power: 0.1424
Test Batch 100/157, reward: 10.757, revenue_rate: 0.2774, distance: 4.5812, memory: 0.0751, power: 0.1391
Test Batch 101/157, reward: 11.004, revenue_rate: 0.2865, distance: 4.7823, memory: 0.0631, power: 0.1438
Test Batch 102/157, reward: 10.465, revenue_rate: 0.2728, distance: 4.2529, memory: 0.0601, power: 0.1354
Test Batch 103/157, reward: 11.942, revenue_rate: 0.3086, distance: 4.9587, memory: 0.0232, power: 0.1506
Test Batch 104/157, reward: 10.841, revenue_rate: 0.2800, distance: 4.5801, memory: 0.0314, power: 0.1382
Test Batch 105/157, reward: 12.471, revenue_rate: 0.3211, distance: 5.2025, memory: 0.0171, power: 0.1518
Test Batch 106/157, reward: 11.781, revenue_rate: 0.3042, distance: 4.8161, memory: 0.0366, power: 0.1505
Test Batch 107/157, reward: 13.131, revenue_rate: 0.3421, distance: 5.4082, memory: 0.0279, power: 0.1639
Test Batch 108/157, reward: 10.605, revenue_rate: 0.2740, distance: 4.3788, memory: 0.0521, power: 0.1342
Test Batch 109/157, reward: 10.965, revenue_rate: 0.2865, distance: 4.8071, memory: 0.0646, power: 0.1489
Test Batch 110/157, reward: 11.344, revenue_rate: 0.2932, distance: 4.7209, memory: 0.0459, power: 0.1415
Test Batch 111/157, reward: 11.815, revenue_rate: 0.3058, distance: 5.0383, memory: 0.0463, power: 0.1475
Test Batch 112/157, reward: 11.947, revenue_rate: 0.3105, distance: 5.2981, memory: 0.0443, power: 0.1571
Test Batch 113/157, reward: 13.314, revenue_rate: 0.3431, distance: 5.7225, memory: 0.0466, power: 0.1679
Test Batch 114/157, reward: 11.699, revenue_rate: 0.3031, distance: 4.7402, memory: 0.0132, power: 0.1423
Test Batch 115/157, reward: 10.919, revenue_rate: 0.2862, distance: 4.7327, memory: 0.0681, power: 0.1450
Test Batch 116/157, reward: 10.716, revenue_rate: 0.2807, distance: 4.5903, memory: 0.0630, power: 0.1368
Test Batch 117/157, reward: 11.561, revenue_rate: 0.3012, distance: 4.7180, memory: 0.0647, power: 0.1485
Test Batch 118/157, reward: 11.985, revenue_rate: 0.3117, distance: 4.9744, memory: 0.0492, power: 0.1534
Test Batch 119/157, reward: 12.705, revenue_rate: 0.3311, distance: 5.5519, memory: 0.0527, power: 0.1662
Test Batch 120/157, reward: 10.904, revenue_rate: 0.2812, distance: 4.5694, memory: 0.0638, power: 0.1393
Test Batch 121/157, reward: 12.357, revenue_rate: 0.3156, distance: 4.9794, memory: 0.0321, power: 0.1553
Test Batch 122/157, reward: 12.427, revenue_rate: 0.3207, distance: 5.2427, memory: 0.0346, power: 0.1557
Test Batch 123/157, reward: 11.410, revenue_rate: 0.2966, distance: 4.8833, memory: 0.0409, power: 0.1433
Test Batch 124/157, reward: 11.331, revenue_rate: 0.2933, distance: 4.7874, memory: 0.0376, power: 0.1465
Test Batch 125/157, reward: 10.241, revenue_rate: 0.2698, distance: 4.5167, memory: 0.0784, power: 0.1392
Test Batch 126/157, reward: 10.398, revenue_rate: 0.2735, distance: 4.4893, memory: 0.0473, power: 0.1359
Test Batch 127/157, reward: 10.321, revenue_rate: 0.2664, distance: 4.2869, memory: 0.0386, power: 0.1351
Test Batch 128/157, reward: 10.517, revenue_rate: 0.2711, distance: 4.4750, memory: 0.0401, power: 0.1353
Test Batch 129/157, reward: 11.441, revenue_rate: 0.2960, distance: 4.9691, memory: 0.0859, power: 0.1474
Test Batch 130/157, reward: 10.945, revenue_rate: 0.2794, distance: 4.5094, memory: 0.0565, power: 0.1386
Test Batch 131/157, reward: 10.751, revenue_rate: 0.2734, distance: 4.1432, memory: 0.0087, power: 0.1300
Test Batch 132/157, reward: 11.116, revenue_rate: 0.2892, distance: 4.6113, memory: 0.0166, power: 0.1395
Test Batch 133/157, reward: 11.982, revenue_rate: 0.3117, distance: 4.8137, memory: -0.0037, power: 0.1510
Test Batch 134/157, reward: 10.022, revenue_rate: 0.2588, distance: 4.2975, memory: 0.0328, power: 0.1295
Test Batch 135/157, reward: 11.140, revenue_rate: 0.2901, distance: 4.7942, memory: 0.0699, power: 0.1464
Test Batch 136/157, reward: 10.298, revenue_rate: 0.2667, distance: 4.3770, memory: 0.0277, power: 0.1268
Test Batch 137/157, reward: 11.069, revenue_rate: 0.2844, distance: 4.4255, memory: 0.0419, power: 0.1406
Test Batch 138/157, reward: 10.773, revenue_rate: 0.2785, distance: 4.5400, memory: 0.0306, power: 0.1327
Test Batch 139/157, reward: 11.494, revenue_rate: 0.2938, distance: 4.8324, memory: 0.0619, power: 0.1465
Test Batch 140/157, reward: 10.819, revenue_rate: 0.2819, distance: 4.6634, memory: 0.0510, power: 0.1416
Test Batch 141/157, reward: 11.894, revenue_rate: 0.3123, distance: 5.0694, memory: 0.0669, power: 0.1540
Test Batch 142/157, reward: 12.066, revenue_rate: 0.3127, distance: 5.0520, memory: 0.0402, power: 0.1534
Test Batch 143/157, reward: 12.211, revenue_rate: 0.3181, distance: 5.1258, memory: 0.0596, power: 0.1521
Test Batch 144/157, reward: 11.558, revenue_rate: 0.2977, distance: 4.5724, memory: 0.0642, power: 0.1434
Test Batch 145/157, reward: 11.452, revenue_rate: 0.2963, distance: 4.7721, memory: 0.0475, power: 0.1419
Test Batch 146/157, reward: 12.477, revenue_rate: 0.3252, distance: 5.3532, memory: 0.0468, power: 0.1549
Test Batch 147/157, reward: 10.826, revenue_rate: 0.2820, distance: 4.6028, memory: 0.0502, power: 0.1423
Test Batch 148/157, reward: 11.660, revenue_rate: 0.2970, distance: 4.7351, memory: 0.0750, power: 0.1476
Test Batch 149/157, reward: 10.617, revenue_rate: 0.2723, distance: 4.3777, memory: 0.0532, power: 0.1341
Test Batch 150/157, reward: 12.222, revenue_rate: 0.3165, distance: 5.1321, memory: 0.0548, power: 0.1596
Test Batch 151/157, reward: 13.176, revenue_rate: 0.3430, distance: 5.5515, memory: 0.0595, power: 0.1680
Test Batch 152/157, reward: 11.979, revenue_rate: 0.3130, distance: 5.2169, memory: 0.0595, power: 0.1570
Test Batch 153/157, reward: 11.878, revenue_rate: 0.3071, distance: 4.8124, memory: 0.0565, power: 0.1463
Test Batch 154/157, reward: 10.658, revenue_rate: 0.2733, distance: 4.2260, memory: 0.0496, power: 0.1343
Test Batch 155/157, reward: 11.960, revenue_rate: 0.3104, distance: 5.0204, memory: 0.0346, power: 0.1535
Test Batch 156/157, reward: 10.037, revenue_rate: 0.2573, distance: 3.9678, memory: 0.0292, power: 0.1292
Test Summary - Avg reward: 11.322, revenue_rate: 0.2933, distance: 4.7498, memory: 0.0486, power: 0.1443
验证完成 - Epoch 3, reward: 11.322, revenue_rate: 0.2933, distance: 4.7498, memory: 0.0486, power: 0.1443
训练完成

[重规划系统统计]
  总重规划事件: 4683
  平均响应时间: 5.102秒
  最大响应时间: 11.700秒
  成功率: 0.00%

开始测试模型...
Test Batch 0/157, reward: 11.111, revenue_rate: 0.2853, distance: 4.5510, memory: 0.0592, power: 0.1363
Test Batch 1/157, reward: 11.730, revenue_rate: 0.3047, distance: 4.7100, memory: 0.0630, power: 0.1452
Test Batch 2/157, reward: 10.086, revenue_rate: 0.2589, distance: 4.0228, memory: 0.0156, power: 0.1241
Test Batch 3/157, reward: 10.511, revenue_rate: 0.2729, distance: 4.5390, memory: 0.0524, power: 0.1329
Test Batch 4/157, reward: 10.775, revenue_rate: 0.2822, distance: 4.8252, memory: 0.0772, power: 0.1450
Test Batch 5/157, reward: 10.684, revenue_rate: 0.2775, distance: 4.5609, memory: 0.0589, power: 0.1409
Test Batch 6/157, reward: 12.155, revenue_rate: 0.3086, distance: 4.9216, memory: 0.0405, power: 0.1438
Test Batch 7/157, reward: 10.038, revenue_rate: 0.2576, distance: 4.1799, memory: 0.0650, power: 0.1319
Test Batch 8/157, reward: 10.024, revenue_rate: 0.2602, distance: 4.2664, memory: 0.0408, power: 0.1296
Test Batch 9/157, reward: 10.568, revenue_rate: 0.2754, distance: 4.5136, memory: 0.0638, power: 0.1428
Test Batch 10/157, reward: 11.305, revenue_rate: 0.2857, distance: 4.5407, memory: 0.0264, power: 0.1390
Test Batch 11/157, reward: 11.125, revenue_rate: 0.2836, distance: 4.5595, memory: 0.0647, power: 0.1407
Test Batch 12/157, reward: 11.578, revenue_rate: 0.2939, distance: 4.6334, memory: 0.0301, power: 0.1432
Test Batch 13/157, reward: 10.671, revenue_rate: 0.2822, distance: 4.8377, memory: 0.0783, power: 0.1459
Test Batch 14/157, reward: 12.060, revenue_rate: 0.3149, distance: 5.3111, memory: 0.0443, power: 0.1520
Test Batch 15/157, reward: 11.670, revenue_rate: 0.3043, distance: 5.0378, memory: 0.0738, power: 0.1508
Test Batch 16/157, reward: 9.656, revenue_rate: 0.2538, distance: 4.1955, memory: 0.0540, power: 0.1259
Test Batch 17/157, reward: 11.309, revenue_rate: 0.2931, distance: 4.8282, memory: 0.1106, power: 0.1491
Test Batch 18/157, reward: 10.764, revenue_rate: 0.2801, distance: 4.4678, memory: 0.0679, power: 0.1392
Test Batch 19/157, reward: 10.675, revenue_rate: 0.2734, distance: 4.3740, memory: 0.0519, power: 0.1340
Test Batch 20/157, reward: 12.047, revenue_rate: 0.3129, distance: 5.0859, memory: 0.0478, power: 0.1504
Test Batch 21/157, reward: 11.810, revenue_rate: 0.3109, distance: 5.2423, memory: 0.0758, power: 0.1519
Test Batch 22/157, reward: 10.788, revenue_rate: 0.2782, distance: 4.4035, memory: 0.0323, power: 0.1350
Test Batch 23/157, reward: 9.921, revenue_rate: 0.2615, distance: 4.5130, memory: 0.0922, power: 0.1371
Test Batch 24/157, reward: 11.562, revenue_rate: 0.2980, distance: 4.8134, memory: 0.0391, power: 0.1417
Test Batch 25/157, reward: 12.160, revenue_rate: 0.3191, distance: 5.2276, memory: 0.0607, power: 0.1571
Test Batch 26/157, reward: 10.835, revenue_rate: 0.2821, distance: 4.5502, memory: 0.0217, power: 0.1396
Test Batch 27/157, reward: 10.597, revenue_rate: 0.2787, distance: 4.6837, memory: 0.0764, power: 0.1406
Test Batch 28/157, reward: 12.366, revenue_rate: 0.3192, distance: 5.0503, memory: 0.0291, power: 0.1540
Test Batch 29/157, reward: 11.541, revenue_rate: 0.2938, distance: 4.6726, memory: 0.0622, power: 0.1465
Test Batch 30/157, reward: 12.475, revenue_rate: 0.3261, distance: 5.3018, memory: 0.0748, power: 0.1585
Test Batch 31/157, reward: 11.071, revenue_rate: 0.2853, distance: 4.6301, memory: 0.0402, power: 0.1400
Test Batch 32/157, reward: 12.102, revenue_rate: 0.3124, distance: 4.9598, memory: 0.0698, power: 0.1542
Test Batch 33/157, reward: 10.400, revenue_rate: 0.2704, distance: 4.4099, memory: 0.0585, power: 0.1355
Test Batch 34/157, reward: 11.109, revenue_rate: 0.2894, distance: 4.8310, memory: 0.0824, power: 0.1458
Test Batch 35/157, reward: 11.814, revenue_rate: 0.3060, distance: 4.9447, memory: 0.0349, power: 0.1508
Test Batch 36/157, reward: 10.731, revenue_rate: 0.2792, distance: 4.5487, memory: 0.0648, power: 0.1366
Test Batch 37/157, reward: 10.592, revenue_rate: 0.2752, distance: 4.3417, memory: 0.0516, power: 0.1346
Test Batch 38/157, reward: 12.072, revenue_rate: 0.3132, distance: 5.2840, memory: 0.0606, power: 0.1589
Test Batch 39/157, reward: 11.102, revenue_rate: 0.2887, distance: 4.8291, memory: 0.0883, power: 0.1451
Test Batch 40/157, reward: 11.761, revenue_rate: 0.3042, distance: 4.8685, memory: 0.0448, power: 0.1518
Test Batch 41/157, reward: 11.267, revenue_rate: 0.2874, distance: 4.5309, memory: 0.0413, power: 0.1400
Test Batch 42/157, reward: 11.007, revenue_rate: 0.2858, distance: 4.6253, memory: 0.0379, power: 0.1409
Test Batch 43/157, reward: 10.439, revenue_rate: 0.2695, distance: 4.5018, memory: 0.0497, power: 0.1352
Test Batch 44/157, reward: 11.694, revenue_rate: 0.2966, distance: 4.6801, memory: 0.0302, power: 0.1425
Test Batch 45/157, reward: 11.139, revenue_rate: 0.2897, distance: 4.6116, memory: 0.0230, power: 0.1366
Test Batch 46/157, reward: 11.653, revenue_rate: 0.2988, distance: 4.7776, memory: 0.0513, power: 0.1493
Test Batch 47/157, reward: 10.822, revenue_rate: 0.2804, distance: 4.4606, memory: 0.0465, power: 0.1404
Test Batch 48/157, reward: 11.166, revenue_rate: 0.2873, distance: 4.5516, memory: 0.0288, power: 0.1373
Test Batch 49/157, reward: 12.490, revenue_rate: 0.3206, distance: 5.2199, memory: 0.0967, power: 0.1575
Test Batch 50/157, reward: 11.674, revenue_rate: 0.2997, distance: 4.7157, memory: 0.0426, power: 0.1446
Test Batch 51/157, reward: 10.286, revenue_rate: 0.2693, distance: 4.5048, memory: 0.0502, power: 0.1320
Test Batch 52/157, reward: 12.650, revenue_rate: 0.3249, distance: 5.1811, memory: 0.0568, power: 0.1588
Test Batch 53/157, reward: 11.943, revenue_rate: 0.3101, distance: 5.0721, memory: 0.0438, power: 0.1550
Test Batch 54/157, reward: 11.602, revenue_rate: 0.3024, distance: 4.8464, memory: 0.0900, power: 0.1445
Test Batch 55/157, reward: 11.387, revenue_rate: 0.2928, distance: 4.7519, memory: 0.0624, power: 0.1459
Test Batch 56/157, reward: 10.285, revenue_rate: 0.2665, distance: 4.3669, memory: 0.0339, power: 0.1289
Test Batch 57/157, reward: 10.671, revenue_rate: 0.2755, distance: 4.5499, memory: 0.0823, power: 0.1384
Test Batch 58/157, reward: 10.999, revenue_rate: 0.2818, distance: 4.4834, memory: 0.0534, power: 0.1395
Test Batch 59/157, reward: 11.000, revenue_rate: 0.2837, distance: 4.6784, memory: 0.0541, power: 0.1401
Test Batch 60/157, reward: 11.815, revenue_rate: 0.3116, distance: 5.0257, memory: 0.0527, power: 0.1536
Test Batch 61/157, reward: 11.458, revenue_rate: 0.2952, distance: 4.8271, memory: 0.0662, power: 0.1509
Test Batch 62/157, reward: 10.897, revenue_rate: 0.2762, distance: 4.3976, memory: 0.0397, power: 0.1333
Test Batch 63/157, reward: 12.538, revenue_rate: 0.3246, distance: 5.2491, memory: 0.0607, power: 0.1577
Test Batch 64/157, reward: 10.543, revenue_rate: 0.2751, distance: 4.3406, memory: 0.0321, power: 0.1325
Test Batch 65/157, reward: 11.168, revenue_rate: 0.2923, distance: 4.8116, memory: 0.0992, power: 0.1502
Test Batch 66/157, reward: 11.949, revenue_rate: 0.3073, distance: 4.9008, memory: 0.0452, power: 0.1498
Test Batch 67/157, reward: 11.792, revenue_rate: 0.3050, distance: 5.0041, memory: 0.0458, power: 0.1468
Test Batch 68/157, reward: 10.692, revenue_rate: 0.2751, distance: 4.4018, memory: 0.0551, power: 0.1341
Test Batch 69/157, reward: 11.673, revenue_rate: 0.3009, distance: 4.9503, memory: 0.0439, power: 0.1467
Test Batch 70/157, reward: 11.304, revenue_rate: 0.2930, distance: 4.8924, memory: 0.0588, power: 0.1432
Test Batch 71/157, reward: 13.452, revenue_rate: 0.3461, distance: 5.4972, memory: 0.0537, power: 0.1679
Test Batch 72/157, reward: 11.137, revenue_rate: 0.2854, distance: 4.5592, memory: 0.0426, power: 0.1371
Test Batch 73/157, reward: 11.370, revenue_rate: 0.2917, distance: 4.6625, memory: 0.0399, power: 0.1420
Test Batch 74/157, reward: 10.462, revenue_rate: 0.2691, distance: 4.3358, memory: 0.0569, power: 0.1366
Test Batch 75/157, reward: 10.780, revenue_rate: 0.2755, distance: 4.4586, memory: 0.0078, power: 0.1292
Test Batch 76/157, reward: 11.369, revenue_rate: 0.2865, distance: 4.3959, memory: 0.0394, power: 0.1392
Test Batch 77/157, reward: 11.096, revenue_rate: 0.2917, distance: 5.1348, memory: 0.0859, power: 0.1457
Test Batch 78/157, reward: 11.335, revenue_rate: 0.2947, distance: 4.7423, memory: 0.0470, power: 0.1465
Test Batch 79/157, reward: 10.505, revenue_rate: 0.2709, distance: 4.2745, memory: 0.0558, power: 0.1307
Test Batch 80/157, reward: 10.546, revenue_rate: 0.2727, distance: 4.4659, memory: 0.0361, power: 0.1338
Test Batch 81/157, reward: 10.314, revenue_rate: 0.2686, distance: 4.4159, memory: 0.0713, power: 0.1356
Test Batch 82/157, reward: 12.722, revenue_rate: 0.3257, distance: 5.2469, memory: 0.0597, power: 0.1587
Test Batch 83/157, reward: 11.346, revenue_rate: 0.2875, distance: 4.5635, memory: 0.0224, power: 0.1377
Test Batch 84/157, reward: 12.741, revenue_rate: 0.3302, distance: 5.3350, memory: 0.0289, power: 0.1608
Test Batch 85/157, reward: 12.123, revenue_rate: 0.3166, distance: 5.1887, memory: 0.0198, power: 0.1556
Test Batch 86/157, reward: 10.926, revenue_rate: 0.2831, distance: 4.5582, memory: -0.0083, power: 0.1366
Test Batch 87/157, reward: 11.463, revenue_rate: 0.2998, distance: 5.0671, memory: 0.0622, power: 0.1508
Test Batch 88/157, reward: 11.997, revenue_rate: 0.3093, distance: 5.0815, memory: 0.0214, power: 0.1470
Test Batch 89/157, reward: 12.779, revenue_rate: 0.3315, distance: 5.4372, memory: 0.0875, power: 0.1647
Test Batch 90/157, reward: 11.862, revenue_rate: 0.3064, distance: 4.7622, memory: 0.0202, power: 0.1468
Test Batch 91/157, reward: 11.062, revenue_rate: 0.2858, distance: 4.7958, memory: 0.0770, power: 0.1459
Test Batch 92/157, reward: 10.890, revenue_rate: 0.2823, distance: 4.7270, memory: 0.0485, power: 0.1374
Test Batch 93/157, reward: 11.371, revenue_rate: 0.2967, distance: 4.8866, memory: 0.0722, power: 0.1529
Test Batch 94/157, reward: 12.837, revenue_rate: 0.3268, distance: 5.2005, memory: 0.0560, power: 0.1629
Test Batch 95/157, reward: 10.748, revenue_rate: 0.2799, distance: 4.6486, memory: 0.0647, power: 0.1384
Test Batch 96/157, reward: 10.487, revenue_rate: 0.2722, distance: 4.3546, memory: 0.0373, power: 0.1300
Test Batch 97/157, reward: 11.155, revenue_rate: 0.2875, distance: 4.6505, memory: 0.0369, power: 0.1370
Test Batch 98/157, reward: 11.980, revenue_rate: 0.3098, distance: 5.0916, memory: 0.0694, power: 0.1577
Test Batch 99/157, reward: 11.360, revenue_rate: 0.2915, distance: 4.6446, memory: 0.0355, power: 0.1410
Test Batch 100/157, reward: 10.760, revenue_rate: 0.2802, distance: 4.4457, memory: 0.0626, power: 0.1405
Test Batch 101/157, reward: 11.873, revenue_rate: 0.3077, distance: 4.9025, memory: 0.0403, power: 0.1484
Test Batch 102/157, reward: 10.040, revenue_rate: 0.2556, distance: 4.0726, memory: 0.0192, power: 0.1248
Test Batch 103/157, reward: 12.732, revenue_rate: 0.3282, distance: 5.3128, memory: 0.0513, power: 0.1597
Test Batch 104/157, reward: 11.876, revenue_rate: 0.3055, distance: 4.8935, memory: 0.0587, power: 0.1494
Test Batch 105/157, reward: 11.539, revenue_rate: 0.2984, distance: 4.8043, memory: 0.0719, power: 0.1520
Test Batch 106/157, reward: 12.040, revenue_rate: 0.3065, distance: 4.8834, memory: 0.0615, power: 0.1498
Test Batch 107/157, reward: 11.588, revenue_rate: 0.2999, distance: 4.9206, memory: 0.0476, power: 0.1522
Test Batch 108/157, reward: 12.197, revenue_rate: 0.3125, distance: 5.0321, memory: 0.0260, power: 0.1509
Test Batch 109/157, reward: 11.067, revenue_rate: 0.2905, distance: 4.9644, memory: 0.0386, power: 0.1406
Test Batch 110/157, reward: 10.710, revenue_rate: 0.2787, distance: 4.3246, memory: 0.0412, power: 0.1338
Test Batch 111/157, reward: 11.199, revenue_rate: 0.2941, distance: 4.8026, memory: 0.0545, power: 0.1440
Test Batch 112/157, reward: 11.471, revenue_rate: 0.2951, distance: 4.8847, memory: 0.0492, power: 0.1460
Test Batch 113/157, reward: 11.360, revenue_rate: 0.2967, distance: 4.8406, memory: 0.0374, power: 0.1397
Test Batch 114/157, reward: 10.723, revenue_rate: 0.2786, distance: 4.5738, memory: 0.0466, power: 0.1405
Test Batch 115/157, reward: 10.616, revenue_rate: 0.2763, distance: 4.5959, memory: 0.0362, power: 0.1326
Test Batch 116/157, reward: 10.299, revenue_rate: 0.2674, distance: 4.3706, memory: 0.0565, power: 0.1334
Test Batch 117/157, reward: 10.508, revenue_rate: 0.2756, distance: 4.7128, memory: 0.0700, power: 0.1385
Test Batch 118/157, reward: 11.930, revenue_rate: 0.3079, distance: 4.9552, memory: 0.0206, power: 0.1514
Test Batch 119/157, reward: 11.727, revenue_rate: 0.3080, distance: 5.1018, memory: 0.0771, power: 0.1577
Test Batch 120/157, reward: 11.814, revenue_rate: 0.3036, distance: 5.0320, memory: 0.0647, power: 0.1473
Test Batch 121/157, reward: 10.460, revenue_rate: 0.2720, distance: 4.5156, memory: 0.0640, power: 0.1316
Test Batch 122/157, reward: 12.661, revenue_rate: 0.3271, distance: 5.2426, memory: 0.0608, power: 0.1596
Test Batch 123/157, reward: 10.747, revenue_rate: 0.2804, distance: 4.6986, memory: 0.0525, power: 0.1370
Test Batch 124/157, reward: 11.477, revenue_rate: 0.3016, distance: 4.9811, memory: 0.0574, power: 0.1483
Test Batch 125/157, reward: 11.555, revenue_rate: 0.2977, distance: 4.8354, memory: 0.0456, power: 0.1475
Test Batch 126/157, reward: 9.912, revenue_rate: 0.2558, distance: 4.0671, memory: 0.0384, power: 0.1200
Test Batch 127/157, reward: 10.367, revenue_rate: 0.2649, distance: 4.2284, memory: 0.0401, power: 0.1318
Test Batch 128/157, reward: 10.903, revenue_rate: 0.2850, distance: 4.7296, memory: 0.0695, power: 0.1424
Test Batch 129/157, reward: 12.674, revenue_rate: 0.3309, distance: 5.3046, memory: 0.0284, power: 0.1595
Test Batch 130/157, reward: 11.676, revenue_rate: 0.3015, distance: 4.7773, memory: 0.0440, power: 0.1514
Test Batch 131/157, reward: 10.656, revenue_rate: 0.2827, distance: 4.5924, memory: 0.0404, power: 0.1390
Test Batch 132/157, reward: 11.031, revenue_rate: 0.2838, distance: 4.5429, memory: 0.0357, power: 0.1415
Test Batch 133/157, reward: 10.339, revenue_rate: 0.2715, distance: 4.4690, memory: 0.0757, power: 0.1342
Test Batch 134/157, reward: 11.300, revenue_rate: 0.2913, distance: 4.5513, memory: 0.0237, power: 0.1410
Test Batch 135/157, reward: 10.553, revenue_rate: 0.2714, distance: 4.3546, memory: 0.0502, power: 0.1307
Test Batch 136/157, reward: 11.270, revenue_rate: 0.2965, distance: 4.8080, memory: 0.0414, power: 0.1444
Test Batch 137/157, reward: 12.412, revenue_rate: 0.3258, distance: 5.4132, memory: 0.0566, power: 0.1596
Test Batch 138/157, reward: 12.620, revenue_rate: 0.3250, distance: 5.2200, memory: 0.0631, power: 0.1609
Test Batch 139/157, reward: 11.897, revenue_rate: 0.3045, distance: 4.9354, memory: 0.0621, power: 0.1488
Test Batch 140/157, reward: 11.076, revenue_rate: 0.2826, distance: 4.4165, memory: 0.0006, power: 0.1343
Test Batch 141/157, reward: 12.059, revenue_rate: 0.3139, distance: 5.0405, memory: 0.0570, power: 0.1565
Test Batch 142/157, reward: 10.005, revenue_rate: 0.2626, distance: 4.4094, memory: 0.0562, power: 0.1297
Test Batch 143/157, reward: 12.037, revenue_rate: 0.3133, distance: 5.1112, memory: 0.0443, power: 0.1572
Test Batch 144/157, reward: 13.389, revenue_rate: 0.3425, distance: 5.6346, memory: 0.0561, power: 0.1697
Test Batch 145/157, reward: 11.950, revenue_rate: 0.3143, distance: 5.4324, memory: 0.0665, power: 0.1571
Test Batch 146/157, reward: 11.116, revenue_rate: 0.2880, distance: 4.8516, memory: 0.0676, power: 0.1452
Test Batch 147/157, reward: 10.357, revenue_rate: 0.2709, distance: 4.4939, memory: 0.0580, power: 0.1308
Test Batch 148/157, reward: 10.553, revenue_rate: 0.2759, distance: 4.3854, memory: 0.0386, power: 0.1319
Test Batch 149/157, reward: 12.025, revenue_rate: 0.3106, distance: 5.0572, memory: 0.0680, power: 0.1526
Test Batch 150/157, reward: 12.945, revenue_rate: 0.3319, distance: 5.2851, memory: 0.0189, power: 0.1627
Test Batch 151/157, reward: 10.752, revenue_rate: 0.2785, distance: 4.5461, memory: 0.0210, power: 0.1329
Test Batch 152/157, reward: 11.038, revenue_rate: 0.2840, distance: 4.6479, memory: 0.0271, power: 0.1363
Test Batch 153/157, reward: 10.856, revenue_rate: 0.2801, distance: 4.4029, memory: 0.0372, power: 0.1350
Test Batch 154/157, reward: 12.375, revenue_rate: 0.3241, distance: 5.2587, memory: 0.0578, power: 0.1617
Test Batch 155/157, reward: 11.252, revenue_rate: 0.2937, distance: 4.9191, memory: 0.0458, power: 0.1474
Test Batch 156/157, reward: 9.023, revenue_rate: 0.2340, distance: 3.7652, memory: 0.0286, power: 0.1096
Test Summary - Avg reward: 11.316, revenue_rate: 0.2929, distance: 4.7596, memory: 0.0511, power: 0.1440
测试完成 - 平均星座收益率: 0.2929

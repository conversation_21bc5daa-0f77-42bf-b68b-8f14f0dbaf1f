"""
Transformer模块实现，用于增强卫星星座任务规划模型
"""
import torch
import torch.nn as nn
import torch.nn.functional as F
import math
import numpy as np

device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')


class PositionalEncoding(nn.Module):
    """
    位置编码模块，为序列添加位置信息
    """
    def __init__(self, d_model, max_len=5000, dropout=0.1):
        super(PositionalEncoding, self).__init__()
        self.dropout = nn.Dropout(p=dropout)
        
        # 创建位置编码矩阵
        pe = torch.zeros(max_len, d_model)
        position = torch.arange(0, max_len, dtype=torch.float).unsqueeze(1)
        
        # 计算div_term用于sin和cos函数
        div_term = torch.exp(torch.arange(0, d_model, 2).float() * 
                           (-math.log(10000.0) / d_model))
        
        pe[:, 0::2] = torch.sin(position * div_term)
        pe[:, 1::2] = torch.cos(position * div_term)
        pe = pe.unsqueeze(0).transpose(0, 1)
        
        # 注册为buffer，不参与梯度更新
        self.register_buffer('pe', pe)
    
    def forward(self, x):
        """
        Args:
            x: Tensor, shape [seq_len, batch_size, embedding_dim]
        """
        x = x + self.pe[:x.size(0), :]
        return self.dropout(x)


class MultiHeadSelfAttention(nn.Module):
    """
    多头自注意力机制
    """
    def __init__(self, d_model, num_heads, dropout=0.1):
        super(MultiHeadSelfAttention, self).__init__()
        assert d_model % num_heads == 0
        
        self.d_model = d_model
        self.num_heads = num_heads
        self.d_k = d_model // num_heads
        
        # 线性变换层
        self.w_q = nn.Linear(d_model, d_model)
        self.w_k = nn.Linear(d_model, d_model)
        self.w_v = nn.Linear(d_model, d_model)
        self.w_o = nn.Linear(d_model, d_model)
        
        # Dropout层
        self.dropout = nn.Dropout(dropout)
        
        # 缩放因子
        self.scale = math.sqrt(self.d_k)
        
        # 初始化参数
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.w_q, self.w_k, self.w_v, self.w_o]:
            nn.init.kaiming_uniform_(module.weight, a=math.sqrt(5))
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, query, key, value, mask=None):
        """
        Args:
            query, key, value: [batch_size, seq_len, d_model]
            mask: [batch_size, seq_len, seq_len] or None
        """
        batch_size, seq_len, d_model = query.size()
        
        # 线性变换并重塑为多头形式
        Q = self.w_q(query).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        K = self.w_k(key).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        V = self.w_v(value).view(batch_size, seq_len, self.num_heads, self.d_k).transpose(1, 2)
        
        # 计算注意力
        attention_output = self._scaled_dot_product_attention(Q, K, V, mask)
        
        # 重塑并通过输出线性层
        attention_output = attention_output.transpose(1, 2).contiguous().view(
            batch_size, seq_len, d_model)
        
        return self.w_o(attention_output)
    
    def _scaled_dot_product_attention(self, Q, K, V, mask=None):
        """
        缩放点积注意力
        """
        # 计算注意力分数
        scores = torch.matmul(Q, K.transpose(-2, -1)) / self.scale
        
        # 应用掩码
        if mask is not None:
            mask = mask.unsqueeze(1).expand(-1, self.num_heads, -1, -1)
            scores.masked_fill_(mask == 0, -1e9)
        
        # 计算注意力权重
        attention_weights = F.softmax(scores, dim=-1)
        attention_weights = self.dropout(attention_weights)
        
        # 应用注意力权重
        return torch.matmul(attention_weights, V)


class FeedForward(nn.Module):
    """
    前馈网络
    """
    def __init__(self, d_model, d_ff, dropout=0.1, activation='gelu'):
        super(FeedForward, self).__init__()
        self.linear1 = nn.Linear(d_model, d_ff)
        self.linear2 = nn.Linear(d_ff, d_model)
        self.dropout = nn.Dropout(dropout)
        
        # 选择激活函数
        if activation == 'relu':
            self.activation = F.relu
        elif activation == 'gelu':
            self.activation = F.gelu
        else:
            raise ValueError(f"Unsupported activation: {activation}")
        
        # 初始化参数
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        for module in [self.linear1, self.linear2]:
            nn.init.kaiming_uniform_(module.weight, a=math.sqrt(5))
            if module.bias is not None:
                nn.init.constant_(module.bias, 0)
    
    def forward(self, x):
        """
        Args:
            x: [batch_size, seq_len, d_model]
        """
        return self.linear2(self.dropout(self.activation(self.linear1(x))))


class TransformerEncoderLayer(nn.Module):
    """
    Transformer编码器层
    """
    def __init__(self, d_model, num_heads, d_ff, dropout=0.1, activation='gelu'):
        super(TransformerEncoderLayer, self).__init__()
        
        # 多头自注意力
        self.self_attention = MultiHeadSelfAttention(d_model, num_heads, dropout)
        
        # 前馈网络
        self.feed_forward = FeedForward(d_model, d_ff, dropout, activation)
        
        # 层归一化
        self.norm1 = nn.LayerNorm(d_model)
        self.norm2 = nn.LayerNorm(d_model)
        
        # Dropout
        self.dropout = nn.Dropout(dropout)
    
    def forward(self, x, mask=None):
        """
        Args:
            x: [batch_size, seq_len, d_model]
            mask: [batch_size, seq_len, seq_len] or None
        """
        # 自注意力 + 残差连接 + 层归一化
        attn_output = self.self_attention(x, x, x, mask)
        x = self.norm1(x + self.dropout(attn_output))
        
        # 前馈网络 + 残差连接 + 层归一化
        ff_output = self.feed_forward(x)
        x = self.norm2(x + self.dropout(ff_output))
        
        return x


class TransformerEncoder(nn.Module):
    """
    Transformer编码器
    """
    def __init__(self, d_model, num_heads, d_ff, num_layers, 
                 max_len=5000, dropout=0.1, activation='gelu'):
        super(TransformerEncoder, self).__init__()
        
        # 位置编码
        self.pos_encoding = PositionalEncoding(d_model, max_len, dropout)
        
        # 编码器层
        self.layers = nn.ModuleList([
            TransformerEncoderLayer(d_model, num_heads, d_ff, dropout, activation)
            for _ in range(num_layers)
        ])
        
        # 最终层归一化
        self.norm = nn.LayerNorm(d_model)
        
        self.num_layers = num_layers
    
    def forward(self, x, mask=None):
        """
        Args:
            x: [batch_size, seq_len, d_model]
            mask: [batch_size, seq_len, seq_len] or None
        """
        # 添加位置编码
        x = x.transpose(0, 1)  # [seq_len, batch_size, d_model]
        x = self.pos_encoding(x)
        x = x.transpose(0, 1)  # [batch_size, seq_len, d_model]
        
        # 通过编码器层
        for layer in self.layers:
            x = layer(x, mask)
        
        # 最终层归一化
        x = self.norm(x)
        
        return x


class ConstellationTransformer(nn.Module):
    """
    专门为卫星星座任务规划设计的Transformer模块
    """
    def __init__(self, input_size, d_model, num_heads, d_ff, num_layers,
                 num_satellites, max_len=5000, dropout=0.1, activation='gelu'):
        super(ConstellationTransformer, self).__init__()
        
        self.d_model = d_model
        self.num_satellites = num_satellites
        
        # 输入投影层
        self.input_projection = nn.Linear(input_size, d_model)
        
        # Transformer编码器
        self.transformer = TransformerEncoder(
            d_model, num_heads, d_ff, num_layers, 
            max_len, dropout, activation
        )
        
        # 卫星特定的输出投影
        self.satellite_projections = nn.ModuleList([
            nn.Linear(d_model, d_model) for _ in range(num_satellites)
        ])
        
        # 全局特征融合
        self.global_fusion = nn.Sequential(
            nn.Linear(d_model * num_satellites, d_model),
            nn.LayerNorm(d_model),
            nn.GELU() if activation == 'gelu' else nn.ReLU(),
            nn.Dropout(dropout)
        )
        
        # 初始化参数
        self._init_weights()
    
    def _init_weights(self):
        """初始化权重"""
        nn.init.kaiming_uniform_(self.input_projection.weight, a=math.sqrt(5))
        if self.input_projection.bias is not None:
            nn.init.constant_(self.input_projection.bias, 0)
        
        for proj in self.satellite_projections:
            nn.init.kaiming_uniform_(proj.weight, a=math.sqrt(5))
            if proj.bias is not None:
                nn.init.constant_(proj.bias, 0)
    
    def forward(self, x, mask=None):
        """
        Args:
            x: [batch_size, seq_len, input_size] 或 
               [batch_size, seq_len, input_size, num_satellites]
            mask: [batch_size, seq_len, seq_len] or None
        """
        if len(x.shape) == 4:  # 多卫星输入
            batch_size, seq_len, input_size, num_satellites = x.shape
            
            # 处理每颗卫星的数据
            satellite_outputs = []
            for sat_idx in range(num_satellites):
                sat_input = x[:, :, :, sat_idx]  # [batch_size, seq_len, input_size]
                
                # 输入投影
                sat_projected = self.input_projection(sat_input)
                
                # Transformer编码
                sat_encoded = self.transformer(sat_projected, mask)
                
                # 卫星特定投影
                sat_output = self.satellite_projections[sat_idx](sat_encoded)
                satellite_outputs.append(sat_output)
            
            # 全局特征融合
            # 将所有卫星的输出在最后一个维度上拼接
            global_features = torch.cat(satellite_outputs, dim=-1)  # [batch_size, seq_len, d_model * num_satellites]
            fused_output = self.global_fusion(global_features)  # [batch_size, seq_len, d_model]
            
            return fused_output, satellite_outputs
        
        else:  # 单一输入
            # 输入投影
            x_projected = self.input_projection(x)
            
            # Transformer编码
            output = self.transformer(x_projected, mask)
            
            return output

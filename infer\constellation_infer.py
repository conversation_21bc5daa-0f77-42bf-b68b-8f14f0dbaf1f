import sys
import os
import csv

# 添加项目根目录到系统路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

import torch
from torch.utils.data import DataLoader
from constellation_smp.gpn_constellation import GPNConstellation
from constellation_smp.constellation_smp import ConstellationSMPDataset, reward, render
import time
import datetime
import numpy as np
import os
from hyperparameter import args as hyperparameter_args

# 推理参数
CHECKPOINT_PATH = 'constellation_smp/constellation_smp100/constellation_gpnindrnn2025_08_11_16_25_26'
NUM_SATELLITES = 3
MEMORY_TOTAL = 0.3
POWER_TOTAL = 5
INFER_NUM_DATA = 100
SEED = 12348
BATCH_SIZE = 1
RENDER_RESULTS = True
OUTPUT_DIR = 'infer_constellation'

# 定义要测试的任务节点数量列表
NODE_SCALES = [100, 200, 300, 400, 500, 750, 1000, 1250, 1500, 2000]

def get_model_config_from_checkpoint(checkpoint_path):
    """
    从检查点目录的log.txt文件中读取并解析模型配置。
    返回: (constellation_mode, use_transformer, transformer_config)
    """
    log_file_path = os.path.join(checkpoint_path, 'log.txt')
    default_mode = 'competitive'  # 默认模式
    default_transformer = False
    default_transformer_config = None

    if not os.path.exists(log_file_path):
        print(f"警告: 在 {checkpoint_path} 中未找到 log.txt。使用默认配置。")
        return default_mode, default_transformer, default_transformer_config
    
    constellation_mode = default_mode
    use_transformer = default_transformer
    transformer_config = {}

    try:
        # 尝试多种编码方式
        encodings = ['utf-8', 'gbk', 'gb2312', 'latin-1']
        content = None

        for encoding in encodings:
            try:
                with open(log_file_path, 'r', encoding=encoding) as f:
                    content = f.read()
                    break
            except UnicodeDecodeError:
                continue

        if content is None:
            raise Exception("无法使用任何编码方式读取日志文件")

        for line in content.split('\n'):
            for line in f:
                line = line.strip()

                # 解析星座模式
                if '星座模式' in line or 'constellation mode' in line.lower() or 'constellation_mode:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        mode = parts[1].strip().lower()
                        if mode in ['cooperative', 'competitive', 'hybrid']:
                            constellation_mode = mode
                            print(f"从检查点自动检测到星座模式: {mode}")

                # 解析Transformer配置
                elif 'use_transformer:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        use_transformer = parts[1].strip().lower() == 'true'

                elif 'transformer_layers:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['num_layers'] = int(parts[1].strip())

                elif 'transformer_heads:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['num_heads'] = int(parts[1].strip())

                elif 'transformer_d_model:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['d_model'] = int(parts[1].strip())

                elif 'transformer_d_ff:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['d_ff'] = int(parts[1].strip())

                elif 'transformer_dropout:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['dropout'] = float(parts[1].strip())

                elif 'transformer_activation:' in line:
                    parts = line.split(':')
                    if len(parts) > 1:
                        transformer_config['activation'] = parts[1].strip()

    except Exception as e:
        print(f"读取或解析 log.txt 时出错: {e}。使用默认配置。")

    # 如果启用了Transformer但缺少配置，使用默认值
    if use_transformer and not transformer_config:
        transformer_config = {
            'd_model': hyperparameter_args.hidden_size,
            'num_heads': 8,
            'd_ff': hyperparameter_args.hidden_size * 4,
            'num_layers': 4,
            'max_len': 5000,
            'dropout': 0.1,
            'activation': 'gelu'
        }
        print("使用默认Transformer配置")

    print(f"检测到配置: 星座模式={constellation_mode}, 使用Transformer={use_transformer}")
    if use_transformer:
        print(f"Transformer配置: {transformer_config}")

    return constellation_mode, use_transformer, transformer_config


def detect_transformer_from_weights(checkpoint_path):
    """
    通过检查权重文件中的键来检测是否使用了Transformer
    """
    actor_path = os.path.join(checkpoint_path, 'actor.pt')

    if not os.path.exists(actor_path):
        return False, None

    try:
        # 加载权重文件
        state_dict = torch.load(actor_path, map_location='cpu')

        # 检查是否包含Transformer相关的键
        transformer_keys = [
            'constellation_encoder.transformer.input_projection.weight',
            'constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight',
            'constellation_encoder.transformer_projection.weight'
        ]

        has_transformer = any(key in state_dict for key in transformer_keys)

        if has_transformer:
            # 尝试从权重文件推断Transformer配置
            transformer_config = {}

            # 推断层数
            layer_count = 0
            while f'constellation_encoder.transformer.transformer.layers.{layer_count}.self_attention.w_q.weight' in state_dict:
                layer_count += 1
            transformer_config['num_layers'] = layer_count

            # 推断模型维度
            if 'constellation_encoder.transformer.input_projection.weight' in state_dict:
                d_model = state_dict['constellation_encoder.transformer.input_projection.weight'].shape[0]
                transformer_config['d_model'] = d_model

            # 推断注意力头数
            if 'constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight' in state_dict:
                q_weight = state_dict['constellation_encoder.transformer.transformer.layers.0.self_attention.w_q.weight']
                d_model = q_weight.shape[0]
                # 假设每个头的维度是64
                num_heads = d_model // 64 if d_model % 64 == 0 else 8
                transformer_config['num_heads'] = num_heads

            # 推断前馈网络维度
            if 'constellation_encoder.transformer.transformer.layers.0.feed_forward.linear1.weight' in state_dict:
                d_ff = state_dict['constellation_encoder.transformer.transformer.layers.0.feed_forward.linear1.weight'].shape[0]
                transformer_config['d_ff'] = d_ff

            # 设置默认值
            transformer_config.setdefault('dropout', 0.1)
            transformer_config.setdefault('activation', 'gelu')
            transformer_config.setdefault('max_len', 5000)

            print(f"从权重文件检测到Transformer配置: {transformer_config}")
            return True, transformer_config

        return False, None

    except Exception as e:
        print(f"检测权重文件时出错: {e}")
        return False, None

def run_inference_for_nodes(num_nodes, device, constellation_mode, use_transformer=False, transformer_config=None):
    """
    为指定数量的节点运行推理
    """
    print(f"\n{'='*20} 开始为 {num_nodes} 个节点在 {constellation_mode} 模式下运行推理 {'='*20}")
    
    # 创建输出目录
    scale_output_dir = os.path.join(OUTPUT_DIR, f'nodes_{num_nodes}')
    if not os.path.exists(scale_output_dir):
        os.makedirs(scale_output_dir)

    # 设置静态和动态大小
    static_size = getattr(hyperparameter_args, 'static_size', 9)
    dynamic_size = getattr(hyperparameter_args, 'dynamic_size', 7)
    
    # 创建测试数据集
    try:
        test_data = ConstellationSMPDataset(
            num_nodes,
            INFER_NUM_DATA,
            SEED,
            MEMORY_TOTAL,
            POWER_TOTAL,
            NUM_SATELLITES
        )
        test_loader = DataLoader(test_data, BATCH_SIZE, False, num_workers=0)
    except Exception as e:
        print(f"创建数据集时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 创建模型
    try:
        actor = GPNConstellation(
            static_size,
            dynamic_size,
            hyperparameter_args.hidden_size,
            NUM_SATELLITES,
            hyperparameter_args.rnn,
            hyperparameter_args.num_layers,
            test_data.update_dynamic,
            test_data.update_mask,
            num_nodes,
            hyperparameter_args.dropout,
            constellation_mode=constellation_mode,  # 显式传递星座模式
            use_transformer=use_transformer,        # 支持Transformer
            transformer_config=transformer_config   # Transformer配置
        ).to(device)

        # 打印模型信息
        total_params = sum(p.numel() for p in actor.parameters())
        print(f"模型参数总数: {total_params:,}")
        if use_transformer:
            print("✓ 使用Transformer增强模型")
        else:
            print("✓ 使用传统模型")

    except Exception as e:
        print(f"创建模型时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 加载模型权重
    try:
        actor_path = os.path.join(CHECKPOINT_PATH, 'actor.pt')
        actor.load_state_dict(torch.load(actor_path, map_location=device))
        actor.eval()
    except Exception as e:
        print(f"加载模型权重时出错 (节点数: {num_nodes}): {e}")
        return
    
    # 创建保存目录
    now = '%s' % datetime.datetime.now().strftime('%Y_%m_%d_%H_%M_%S')
    save_dir = os.path.join(scale_output_dir, f'infer_{now}_{constellation_mode}')
    if not os.path.exists(save_dir):
        os.makedirs(save_dir)
    
    # 创建详细结果的CSV文件
    detailed_csv_path = os.path.join(save_dir, 'inference_details.csv')
    
    # 创建日志文件
    infer_log_path = os.path.join(save_dir, 'infer_log.txt')
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write(f'推理数据数量: {INFER_NUM_DATA}\n')
        f.write(f'每个序列任务数量: {num_nodes}\n')
        f.write(f'星座卫星数量: {NUM_SATELLITES}\n')
        f.write(f'星座模式: {constellation_mode}\n')
        f.write(f'模型: {hyperparameter_args.model}+{hyperparameter_args.rnn}\n')
        f.write(f'种子: {SEED}\n')
        f.write(f'内存总量: {MEMORY_TOTAL}\n')
        f.write(f'电量总量: {POWER_TOTAL}\n')
        f.write(f'检查点路径: {CHECKPOINT_PATH}\n\n')
    
    # 评估指标
    plan_revenue_rates, infer_times, powers = [], [], []
    
    # 使用 'with' 语句确保CSV文件被正确关闭
    with open(detailed_csv_path, 'w', newline='', encoding='utf-8') as csvfile:
        csv_writer = csv.writer(csvfile)
        # 写入CSV表头
        csv_writer.writerow(['batch_idx', 'reward', 'revenue_rate', 'distance', 'memory_usage', 'power_usage', 'inference_time_s'])

        # 遍历测试数据
        for batch_idx, batch in enumerate(test_loader):
            if batch_idx >= INFER_NUM_DATA:
                break
                
            static, dynamic, _ = batch
            static = static.to(device)
            dynamic = dynamic.to(device)
            
            # 记录原始数据以计算最终指标
            static_record = static.clone()
            
            # 打印任务总量信息
            if batch_idx == 0:
                print(f'总收益: {torch.sum(static[:, 4, :], dim=1).item()}')
                print(f'总内存需求: {torch.sum(static[:, 5, :], dim=1).item()}')
                print(f'总电量需求: {torch.sum(static[:, 6, :], dim=1).item()}')
                print(f'每颗卫星内存供应: {MEMORY_TOTAL}')
                print(f'每颗卫星电量供应: {POWER_TOTAL}')
                
            # 推理时间开始
            start_time = time.time()
            
            # 模型推理
            try:
                with torch.no_grad():
                    tour_indices, satellite_indices, _, _ = actor.forward(static, dynamic)
                
                # 计算奖励和其他指标，传入星座模式
                reward_value, revenue_rate, distance, memory, power = reward(static, tour_indices, satellite_indices, constellation_mode)
            except Exception as e:
                print(f"批次 {batch_idx} 推理过程中出错 (节点数: {num_nodes}): {e}")
                continue
            
            # 推理时间结束
            end_time = time.time()
            infer_time = end_time - start_time
            
            # 提取标量值以便记录
            reward_val = reward_value.mean().item()
            revenue_rate_val = revenue_rate.mean().item()
            distance_val = distance.mean().item()
            memory_val = memory.mean().item()
            power_val = power.mean().item()
            
            # 保存指标
            plan_revenue_rates.append(revenue_rate_val)
            powers.append(power_val)
            infer_times.append(infer_time)
            
            # 打印每个批次的结果
            print(f'批次 {batch_idx}/{INFER_NUM_DATA}:')
            print(f'  奖励值: {reward_val:.4f}')
            print(f'  收益率: {revenue_rate_val:.4f}')
            print(f'  距离: {distance_val:.4f}')
            print(f'  内存使用: {memory_val:.4f}')
            print(f'  能量使用: {power_val:.4f}')
            print(f'  推理时间: {infer_time:.4f}秒')
            
            # 记录到日志
            with open(infer_log_path, 'a+', encoding='utf-8') as f:
                f.write(f'批次 {batch_idx + 1}:\n')
                f.write(f'  奖励值: {reward_val:.4f}\n')
                f.write(f'  收益率: {revenue_rate_val:.4f}\n')
                f.write(f'  距离: {distance_val:.4f}\n')
                f.write(f'  内存使用: {memory_val:.4f}\n')
                f.write(f'  能量使用: {power_val:.4f}\n')
                f.write(f'  推理时间: {infer_time:.4f}秒\n\n')

            # 将详细结果写入CSV
            csv_writer.writerow([batch_idx, f'{reward_val:.4f}', f'{revenue_rate_val:.4f}', f'{distance_val:.4f}', f'{memory_val:.4f}', f'{power_val:.4f}', f'{infer_time:.4f}'])

            # 渲染结果
            if RENDER_RESULTS:
                img_save_path = os.path.join(save_dir, f'batch_{batch_idx}_nodes_{num_nodes}.png')
                # 始终渲染批次中的第一个样本（索引为0），并传入卫星数量
                render(static, tour_indices, satellite_indices, img_save_path, NUM_SATELLITES, 0)
            
    # 计算平均指标
    avg_revenue_rate = np.mean(plan_revenue_rates) if plan_revenue_rates else 0
    avg_power = np.mean(powers) if powers else 0
    avg_infer_time = np.mean(infer_times) if infer_times else 0
    
    # 打印和记录总结
    summary = (
        f'推理完成 (节点数: {num_nodes}):\n'
        f'  平均收益率: {avg_revenue_rate:.4f}\n'
        f'  平均能量使用: {avg_power:.4f}\n'
        f'  平均推理时间: {avg_infer_time:.4f}秒'
    )
    print(summary)
    with open(infer_log_path, 'a+', encoding='utf-8') as f:
        f.write('\n' + '='*20 + ' 总结 ' + '='*20 + '\n')
        f.write(f'平均收益率: {avg_revenue_rate:.4f}\n')
        f.write(f'平均能量使用: {avg_power:.4f}\n')
        f.write(f'平均推理时间: {avg_infer_time:.4f}秒\n')

    # 确保在出错时也能返回
    if not plan_revenue_rates:
        return 0, 0, 0
        
    return avg_revenue_rate, avg_power, avg_infer_time

def main():
    # 设置设备
    device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
    print(f"使用设备: {device}")
    
    # 确保检查点路径存在
    if not os.path.exists(CHECKPOINT_PATH) or not os.path.exists(os.path.join(CHECKPOINT_PATH, 'actor.pt')):
        print(f"错误: 检查点路径或actor.pt不存在于: {CHECKPOINT_PATH}")
        return
    
    # 确保根输出目录存在
    if not os.path.exists(OUTPUT_DIR):
        os.makedirs(OUTPUT_DIR)
        
    # 从检查点自动检测模型配置
    constellation_mode, use_transformer, transformer_config = get_model_config_from_checkpoint(CHECKPOINT_PATH)

    # 通过权重文件进一步检测Transformer配置（更可靠）
    weight_has_transformer, weight_transformer_config = detect_transformer_from_weights(CHECKPOINT_PATH)

    if weight_has_transformer:
        print("✓ 权重文件检测到Transformer组件，覆盖日志配置")
        use_transformer = True
        if weight_transformer_config:
            transformer_config = weight_transformer_config
    elif use_transformer:
        print("⚠ 日志显示使用Transformer但权重文件中未找到，可能配置有误")
        use_transformer = False
        transformer_config = None

    print(f"最终配置: 星座模式={constellation_mode}, 使用Transformer={use_transformer}")
    if use_transformer and transformer_config:
        print(f"Transformer配置: {transformer_config}")
        
    # 创建总结果的CSV文件
    summary_csv_path = os.path.join(OUTPUT_DIR, 'inference_summary.csv')
    with open(summary_csv_path, 'w', newline='', encoding='utf-8') as summary_file:
        summary_writer = csv.writer(summary_file)
        summary_writer.writerow(['num_nodes', 'constellation_mode', 'avg_revenue_rate', 'avg_power_usage', 'avg_inference_time_s'])
        
        # 为每个节点规模运行推理
        for num_nodes in NODE_SCALES:
            result = run_inference_for_nodes(num_nodes, device, constellation_mode, use_transformer, transformer_config)
            if result is None:
                print(f"节点数 {num_nodes} 的推理运行失败，跳过...")
                continue
            avg_revenue_rate, avg_power, avg_infer_time = result
            # 将总结结果写入CSV
            summary_writer.writerow([num_nodes, constellation_mode, f'{avg_revenue_rate:.4f}', f'{avg_power:.4f}', f'{avg_infer_time:.4f}'])

if __name__ == '__main__':
    main() 
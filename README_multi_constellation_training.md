# 多星座模式训练脚本使用指南

## 📋 概述

`train_multi_constellation_modes.py` 是一个全自动的多星座模式训练脚本，能够一次性训练三种不同的星座工作模式（cooperative、competitive、hybrid），并生成详细的性能对比分析。

## 🌟 主要功能

### ✅ 自动化训练
- **三种星座模式**：自动训练 cooperative、competitive、hybrid 三种模式
- **智能命名**：自动生成包含模式信息的文件夹名称
- **完整日志**：记录整个训练过程的详细日志
- **错误处理**：优雅处理训练过程中的异常情况

### 📊 性能对比分析
- **对比图表**：自动生成性能指标对比图
- **训练曲线**：展示三种模式的训练过程对比
- **详细报告**：生成JSON和文本格式的分析报告
- **最佳模式识别**：自动识别性能最优的星座模式

### 🔧 配置灵活性
- **超参数继承**：使用 `hyperparameter.py` 中的配置
- **Transformer支持**：支持传统模型和Transformer增强模型
- **自定义参数**：可通过命令行参数调整配置

## 🚀 快速开始

### 基本使用
```bash
# 使用默认配置训练三种星座模式
python train_multi_constellation_modes.py
```

### 使用Transformer增强模型
```bash
# 启用Transformer增强
python train_multi_constellation_modes.py --use_transformer
```

### 查看帮助信息
```bash
python train_multi_constellation_modes.py --help
```

## 📁 输出结构

训练完成后会生成以下目录结构：

```
constellation_smp/
└── constellation_smp100/
    └── multi_constellation_comparison_TIMESTAMP/
        ├── constellation_gpnindrnn_cooperative_TIMESTAMP/
        │   ├── actor.pt                    # Cooperative模式Actor权重
        │   ├── critic.pt                   # Cooperative模式Critic权重
        │   ├── log.txt                     # 训练日志
        │   └── test_cooperative/           # 测试结果
        ├── constellation_gpnindrnn_competitive_TIMESTAMP/
        │   ├── actor.pt                    # Competitive模式权重
        │   ├── critic.pt
        │   ├── log.txt
        │   └── test_competitive/
        ├── constellation_gpnindrnn_hybrid_TIMESTAMP/
        │   ├── actor.pt                    # Hybrid模式权重
        │   ├── critic.pt
        │   ├── log.txt
        │   └── test_hybrid/
        ├── comparison_results/
        │   ├── performance_comparison.png   # 性能对比图表
        │   ├── training_curves_comparison.png # 训练曲线对比
        │   ├── comparison_results.json      # 详细结果数据
        │   └── comparison_report.txt        # 文本格式报告
        └── multi_mode_training_log.txt      # 全局训练日志
```

## 📊 生成的对比分析

### 1. 性能指标对比图
- **最佳奖励**：各模式的最高训练奖励
- **平均收益率**：任务完成效率对比
- **平均距离**：卫星移动成本对比
- **平均内存使用**：资源利用效率
- **平均功耗**：能量消耗对比
- **模型参数数量**：模型复杂度对比

### 2. 训练曲线对比
- **奖励曲线**：训练过程中奖励变化
- **收益率曲线**：收益率提升趋势
- **距离曲线**：移动成本优化过程
- **功耗曲线**：能量效率改善

### 3. 详细分析报告
- **实验配置**：记录所有训练参数
- **性能统计**：各项指标的详细数值
- **最佳模式**：自动识别最优配置
- **参数对比**：模型复杂度分析

## ⚙️ 配置说明

### 星座模式说明

#### 🤝 Cooperative（协同模式）
- **特点**：卫星间完全信息共享
- **优势**：全局最优决策，整体性能最佳
- **适用**：需要最大化整体收益的场景

#### 🏁 Competitive（竞争模式）
- **特点**：卫星间无信息交互
- **优势**：分布式决策，鲁棒性强
- **适用**：通信受限或隐私要求高的场景

#### ⚖️ Hybrid（混合模式）
- **特点**：门控信息交互机制
- **优势**：平衡性能与鲁棒性
- **适用**：需要在性能和鲁棒性间权衡的场景

### Transformer配置
当启用 `--use_transformer` 时，会使用以下配置：
- **层数**：`args.transformer_layers`
- **注意力头数**：`args.transformer_heads`
- **模型维度**：`args.transformer_d_model`
- **前馈网络维度**：`args.transformer_d_ff`
- **Dropout率**：`args.transformer_dropout`
- **激活函数**：`args.transformer_activation`

## 🧪 测试验证

在运行完整训练前，建议先运行快速测试：

```bash
# 运行快速功能测试
python test_multi_mode_training_quick.py
```

测试内容包括：
- ✅ 模型创建功能
- ✅ Transformer增强模型
- ✅ 单模式训练流程
- ✅ 目录命名规则

## 📈 性能监控

### 训练过程监控
脚本会实时输出训练进度：
```
================================================================================
开始训练星座模式: COOPERATIVE
================================================================================
cooperative 模式模型信息:
  Actor参数数量: 2,211,337
  Critic参数数量: 1,234,567
  总参数数量: 3,445,904

开始训练 cooperative 模式...
Epoch 1/20: reward=8.5432, revenue_rate=0.8765
...
```

### 结果总结
训练完成后会显示各模式对比：
```
================================================================================
实验总结
================================================================================
COOPERATIVE: 奖励=8.7628, 收益率=0.9087
COMPETITIVE: 奖励=8.2341, 收益率=0.8654
HYBRID: 奖励=8.5123, 收益率=0.8821

🏆 最佳模式: COOPERATIVE (奖励: 8.7628)
```

## 🔧 故障排除

### 常见问题

#### 1. 内存不足
```bash
# 减少批次大小
python train_multi_constellation_modes.py --batch_size 32
```

#### 2. 训练时间过长
```bash
# 减少训练轮数
python train_multi_constellation_modes.py --epochs 10
```

#### 3. GPU不可用
脚本会自动检测并使用CPU，但训练速度会较慢。

### 日志分析
如果训练失败，检查以下日志文件：
- `multi_mode_training_log.txt`：全局训练日志
- 各模式目录下的 `log.txt`：具体模式的训练日志

## 📝 自定义扩展

### 添加新的星座模式
1. 在 `constellation_modes` 列表中添加新模式
2. 确保模型支持该模式
3. 更新对比分析逻辑

### 修改评估指标
1. 修改 `create_comparison_plots` 函数
2. 添加新的指标计算
3. 更新报告生成逻辑

## 🎯 最佳实践

1. **首次使用**：先运行快速测试验证功能
2. **参数调优**：从小规模问题开始，逐步增加复杂度
3. **结果分析**：重点关注收益率和奖励指标
4. **模式选择**：根据具体应用场景选择最适合的星座模式
5. **定期备份**：及时备份重要的训练结果

## 📞 技术支持

如遇到问题，请检查：
1. 依赖包是否正确安装
2. `hyperparameter.py` 配置是否合理
3. 系统资源是否充足
4. 日志文件中的错误信息

---

**祝您训练顺利！🚀**

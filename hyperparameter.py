import argparse

parser = argparse.ArgumentParser(description='Combinatorial Optimization')
# model
# pn
# gpn
parser.add_argument('--model', default='gpn')
# train
parser.add_argument('--task', default='constellation_smp')
parser.add_argument('--seed', default=12346, type=int)  # 12345
parser.add_argument('--checkpoint', default=None)
parser.add_argument('--test', action='store_true', default=False)
parser.add_argument('--max_grad_norm', default=1, type=float)
parser.add_argument('--dropout', default=0.1, type=float)
# 学习率
parser.add_argument('--actor_lr', default=1e-4, type=float)
parser.add_argument('--critic_lr', default=2e-4, type=float)
parser.add_argument('--weight_decay', type=float, default=1e-4, help='Weight decay (L2 regularization)')
parser.add_argument('--train_size', default=100000, type=int)
parser.add_argument('--valid_size', default=10000, type=int)
parser.add_argument('--epochs', default=3, type=int)
parser.add_argument('--lr', type=float, default=2e-4, help="learning rate")
parser.add_argument('--nodes', dest='num_nodes', default=100, type=int)
parser.add_argument('--hidden', dest='hidden_size', default=256, type=int)
parser.add_argument('--batch_size', default=64, type=int)
parser.add_argument('--static_size', default=9, type=int)
parser.add_argument('--dynamic_size', default=7, type=int)

parser.add_argument('--memory_total', default=0.3, type=float)
parser.add_argument('--power_total', default=5, type=float)

# 星座相关参数
parser.add_argument('--num_satellites', default=3, type=int, help='卫星星座中的卫星数量')
parser.add_argument('--constellation_mode', default='hybrid', type=str, 
                    help='星座工作模式: cooperative(协同), competitive(竞争), hybrid(混合)')
parser.add_argument('--task_sharing', action='store_true', default=True, 
                    help='是否允许卫星间共享任务信息')
parser.add_argument('--communication_delay', default=0.01, type=float, 
                    help='星座内卫星间通信延迟')
parser.add_argument('--satellite_distance', default=0.5, type=float,
                    help='卫星间平均距离（归一化）')
parser.add_argument('--verbose', action='store_true', default=True,
                    help='是否打印详细训练信息')

# MultiHead_Additive_Attention
parser.add_argument('--attention', default='MultiHead_Additive_Attention', type=str)
parser.add_argument('--n_head', default=8, type=int)

# lstm
# indrnn
# indrnnv2
parser.add_argument('--rnn', default='indrnn', type=str)
parser.add_argument('--layers', dest='num_layers', default=2, type=int)

# conv1d
parser.add_argument('--encoder', default='conv1d', type=str)

# Transformer相关参数
parser.add_argument('--use_transformer', action='store_true', default=True,
                    help='是否启用Transformer增强模块')
parser.add_argument('--transformer_layers', default=2, type=int,
                    help='Transformer编码器层数')
parser.add_argument('--transformer_heads', default=4, type=int,
                    help='Transformer多头注意力的头数')
parser.add_argument('--transformer_d_model', default=256, type=int,
                    help='Transformer模型维度，默认与hidden_size相同')
parser.add_argument('--transformer_d_ff', default=512, type=int,
                    help='Transformer前馈网络隐藏层维度')
parser.add_argument('--transformer_dropout', default=0.1, type=float,
                    help='Transformer模块的dropout率')
parser.add_argument('--transformer_activation', default='gelu', type=str,
                    choices=['relu', 'gelu'], help='Transformer激活函数')
parser.add_argument('--transformer_max_len', default=5000, type=int,
                    help='Transformer位置编码的最大长度')
parser.add_argument('--transformer_integration_mode', default='parallel', type=str,
                    choices=['parallel', 'sequential', 'hybrid'],
                    help='Transformer集成模式: parallel(并行), sequential(串行), hybrid(混合)')

# 重规划系统参数
parser.add_argument('--enable_replanning', action='store_true', default=True,
                    help='启用动态重规划功能')
parser.add_argument('--maml_inner_lr', default=0.01, type=float,
                    help='MAML内循环学习率')
parser.add_argument('--maml_outer_lr', default=0.001, type=float,
                    help='MAML外循环学习率')
parser.add_argument('--adaptation_steps', default=3, type=int,
                    help='MAML快速适应步数')
parser.add_argument('--replanning_threshold', default=0.15, type=float,
                    help='重规划触发阈值')
parser.add_argument('--performance_threshold', default=0.15, type=float,
                    help='性能下降触发阈值')
parser.add_argument('--environment_threshold', default=0.2, type=float,
                    help='环境变化触发阈值')
parser.add_argument('--resource_threshold', default=0.8, type=float,
                    help='资源约束触发阈值')
parser.add_argument('--emergency_threshold', default=0.1, type=float,
                    help='紧急事件触发阈值')
parser.add_argument('--temporal_threshold', default=0.3, type=float,
                    help='时间窗口触发阈值')
parser.add_argument('--global_trigger_threshold', default=0.5, type=float,
                    help='全局触发阈值')
parser.add_argument('--experience_buffer_size', default=10000, type=int,
                    help='经验回放缓冲区大小')
parser.add_argument('--baseline_window', default=100, type=int,
                    help='性能基线窗口大小')
parser.add_argument('--max_response_time', default=10.0, type=float,
                    help='最大重规划响应时间（秒）')
parser.add_argument('--emergency_response_time', default=3.0, type=float,
                    help='紧急情况最大响应时间（秒）')

# 向后兼容性设置
parser.add_argument('--legacy_mode', action='store_true', default=False,
                    help='启用传统模式，禁用所有新功能以确保完全向后兼容')

args = parser.parse_args()

# 动态调整参数以确保兼容性
if not hasattr(args, 'transformer_d_model') or args.transformer_d_model == 256:
    args.transformer_d_model = args.hidden_size  # 与现有hidden_size保持一致

# 如果启用传统模式，禁用所有新功能
if args.legacy_mode:
    args.use_transformer = False
    args.enable_replanning = False

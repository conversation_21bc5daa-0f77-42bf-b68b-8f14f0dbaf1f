"""
敏捷观察卫星星座任务重规划系统

基于MAML元学习的动态重规划框架，集成现有GPNConstellation架构
实现秒级响应的实时任务重分配和优化

主要模块:
- constellation_replanning_engine: 重规划引擎核心
- maml_replanner: MAML元学习重规划器
- trigger_system: 多层次触发机制
- state_monitor: 星座状态监控器
- experience_replay: 经验回放系统
- adaptive_reward: 自适应奖励管理
- constraint_handler: 约束处理器
"""

__version__ = "1.0.0"
__author__ = "Satellite Constellation Replanning Team"

# 导入核心模块
from .constellation_replanning_engine import ConstellationReplanningEngine
from .maml_replanner import ConstellationMAMLReplanner
from .trigger_system import ConstellationReplanningTrigger
from .state_monitor import ConstellationStateMonitor

__all__ = [
    'ConstellationReplanningEngine',
    'ConstellationMAMLReplanner', 
    'ConstellationReplanningTrigger',
    'ConstellationStateMonitor'
]

"""
星座状态监控器

监控卫星星座的状态变化，检测需要重规划的环境变化
基于现有dynamic_data结构设计状态监控机制
"""

import torch
import numpy as np
from typing import Dict, List, Tuple, Optional
import logging


class ConstellationStateMonitor:
    """
    星座状态监控器
    
    功能:
    1. 监控卫星状态变化
    2. 检测资源约束变化
    3. 识别任务优先级变化
    4. 跟踪通信状态变化
    """
    
    def __init__(self, num_satellites: int, constellation_mode: str = 'hybrid'):
        """
        初始化状态监控器
        
        Args:
            num_satellites: 卫星数量
            constellation_mode: 星座工作模式
        """
        self.num_satellites = num_satellites
        self.constellation_mode = constellation_mode
        self.logger = logging.getLogger(__name__)
        
        # 状态历史
        self.previous_state = None
        self.state_history = []
        self.max_history_length = 100
        
        # 变化检测阈值
        self.change_thresholds = {
            'satellite_position': 0.1,      # 卫星位置变化阈值
            'task_priority': 0.2,           # 任务优先级变化阈值
            'resource_availability': 0.15,  # 资源可用性变化阈值
            'communication_status': 0.05,   # 通信状态变化阈值
            'time_window': 0.1,             # 时间窗口变化阈值
            'access_pattern': 0.2           # 访问模式变化阈值
        }
        
        # 变化统计
        self.change_statistics = {
            'total_changes': 0,
            'change_types': {},
            'change_frequency': []
        }
        
        self.logger.info(f"ConstellationStateMonitor initialized for {num_satellites} satellites")
    
    def detect_changes(self, current_dynamic_state: torch.Tensor) -> Dict[str, float]:
        """
        检测环境变化

        Args:
            current_dynamic_state: 当前动态状态 (batch_size, dynamic_size, seq_len, num_satellites)

        Returns:
            Dict: 检测到的变化字典 {change_type: change_magnitude}
        """
        if self.previous_state is None:
            self.previous_state = current_dynamic_state.clone()
            return {}

        changes = {}

        try:
            # 检查维度兼容性
            if current_dynamic_state.shape != self.previous_state.shape:
                self.logger.warning(f"State shape changed: {self.previous_state.shape} -> {current_dynamic_state.shape}")
                # 重置previous_state以适应新的维度
                self.previous_state = current_dynamic_state.clone()
                return {}

            # 1. 检测卫星状态变化
            satellite_changes = self._detect_satellite_changes(current_dynamic_state)
            changes.update(satellite_changes)

            # 2. 检测资源状态变化
            resource_changes = self._detect_resource_changes(current_dynamic_state)
            changes.update(resource_changes)

            # 3. 检测时间窗口变化
            temporal_changes = self._detect_temporal_changes(current_dynamic_state)
            changes.update(temporal_changes)

            # 4. 检测访问模式变化
            access_changes = self._detect_access_changes(current_dynamic_state)
            changes.update(access_changes)

            # 更新状态历史
            self._update_state_history(current_dynamic_state, changes)

            # 更新统计信息
            self._update_statistics(changes)

            self.previous_state = current_dynamic_state.clone()

        except Exception as e:
            self.logger.error(f"Error in change detection: {e}")
            # 重置状态以避免持续错误
            self.previous_state = current_dynamic_state.clone()
            changes = {}

        return changes
    
    def _detect_satellite_changes(self, current_state: torch.Tensor) -> Dict[str, float]:
        """检测卫星状态变化"""
        changes = {}
        
        for sat_idx in range(self.num_satellites):
            sat_current = current_state[:, :, :, sat_idx]
            sat_previous = self.previous_state[:, :, :, sat_idx]
            
            # 计算状态变化幅度
            state_diff = torch.norm(sat_current - sat_previous, dim=1)
            max_change = torch.max(state_diff)
            
            if max_change > self.change_thresholds['satellite_position']:
                changes[f'satellite_{sat_idx}_position'] = max_change.item()
                self.logger.debug(f"Satellite {sat_idx} position change detected: {max_change:.4f}")
        
        return changes
    
    def _detect_resource_changes(self, current_state: torch.Tensor) -> Dict[str, float]:
        """检测资源状态变化"""
        changes = {}
        
        # 基于现有dynamic_data结构:
        # DynamicData[:, 2, :, :]: memory surplus
        # DynamicData[:, 3, :, :]: power surplus
        
        # 内存资源变化
        memory_current = current_state[:, 2, :, :]
        memory_previous = self.previous_state[:, 2, :, :]
        memory_change = torch.abs(memory_current - memory_previous)
        max_memory_change = torch.max(memory_change)
        
        if max_memory_change > self.change_thresholds['resource_availability']:
            changes['memory_constraint'] = max_memory_change.item()
            self.logger.debug(f"Memory constraint change detected: {max_memory_change:.4f}")
        
        # 功率资源变化
        power_current = current_state[:, 3, :, :]
        power_previous = self.previous_state[:, 3, :, :]
        power_change = torch.abs(power_current - power_previous)
        max_power_change = torch.max(power_change)
        
        if max_power_change > self.change_thresholds['resource_availability']:
            changes['power_constraint'] = max_power_change.item()
            self.logger.debug(f"Power constraint change detected: {max_power_change:.4f}")
        
        return changes
    
    def _detect_temporal_changes(self, current_state: torch.Tensor) -> Dict[str, float]:
        """检测时间窗口变化"""
        changes = {}
        
        # 基于现有dynamic_data结构:
        # DynamicData[:, 0, :, :]: task time windows mark
        
        time_window_current = current_state[:, 0, :, :]
        time_window_previous = self.previous_state[:, 0, :, :]
        time_change = torch.abs(time_window_current - time_window_previous)
        max_time_change = torch.max(time_change)
        
        if max_time_change > self.change_thresholds['time_window']:
            changes['time_window_change'] = max_time_change.item()
            self.logger.debug(f"Time window change detected: {max_time_change:.4f}")
        
        return changes
    
    def _detect_access_changes(self, current_state: torch.Tensor) -> Dict[str, float]:
        """检测访问模式变化"""
        changes = {}
        
        # 基于现有dynamic_data结构:
        # DynamicData[:, 1, :, :]: task access mark
        
        access_current = current_state[:, 1, :, :]
        access_previous = self.previous_state[:, 1, :, :]
        access_change = torch.abs(access_current - access_previous)
        max_access_change = torch.max(access_change)
        
        if max_access_change > self.change_thresholds['access_pattern']:
            changes['access_pattern_change'] = max_access_change.item()
            self.logger.debug(f"Access pattern change detected: {max_access_change:.4f}")
        
        return changes
    
    def _update_state_history(self, current_state: torch.Tensor, changes: Dict[str, float]):
        """更新状态历史"""
        history_entry = {
            'timestamp': torch.tensor(len(self.state_history)),
            'state_summary': self._compute_state_summary(current_state),
            'changes': changes.copy(),
            'change_count': len(changes)
        }
        
        self.state_history.append(history_entry)
        
        # 限制历史长度
        if len(self.state_history) > self.max_history_length:
            self.state_history.pop(0)
    
    def _compute_state_summary(self, state: torch.Tensor) -> Dict[str, float]:
        """计算状态摘要"""
        summary = {
            'mean_memory': torch.mean(state[:, 2, :, :]).item(),
            'mean_power': torch.mean(state[:, 3, :, :]).item(),
            'active_time_windows': torch.sum(state[:, 0, :, :]).item(),
            'active_access_marks': torch.sum(state[:, 1, :, :]).item()
        }
        return summary
    
    def _update_statistics(self, changes: Dict[str, float]):
        """更新变化统计信息"""
        if changes:
            self.change_statistics['total_changes'] += 1
            
            for change_type in changes.keys():
                if change_type not in self.change_statistics['change_types']:
                    self.change_statistics['change_types'][change_type] = 0
                self.change_statistics['change_types'][change_type] += 1
            
            # 记录变化频率
            self.change_statistics['change_frequency'].append(len(changes))
            
            # 限制频率历史长度
            if len(self.change_statistics['change_frequency']) > 1000:
                self.change_statistics['change_frequency'].pop(0)
    
    def get_change_statistics(self) -> Dict[str, any]:
        """获取变化统计信息"""
        stats = self.change_statistics.copy()
        
        if self.change_statistics['change_frequency']:
            stats['avg_change_frequency'] = np.mean(self.change_statistics['change_frequency'])
            stats['max_change_frequency'] = np.max(self.change_statistics['change_frequency'])
        else:
            stats['avg_change_frequency'] = 0
            stats['max_change_frequency'] = 0
        
        return stats
    
    def set_change_threshold(self, change_type: str, threshold: float):
        """设置变化检测阈值"""
        if change_type in self.change_thresholds:
            old_threshold = self.change_thresholds[change_type]
            self.change_thresholds[change_type] = threshold
            self.logger.info(f"Updated {change_type} threshold: {old_threshold} -> {threshold}")
        else:
            self.logger.warning(f"Unknown change type: {change_type}")
    
    def get_state_trend(self, window_size: int = 10) -> Dict[str, float]:
        """获取状态变化趋势"""
        if len(self.state_history) < window_size:
            return {'trend': 'insufficient_data'}
        
        recent_history = self.state_history[-window_size:]
        
        # 计算变化趋势
        change_counts = [entry['change_count'] for entry in recent_history]
        
        if len(change_counts) > 1:
            # 简单线性趋势
            x = np.arange(len(change_counts))
            trend_slope = np.polyfit(x, change_counts, 1)[0]
            
            return {
                'trend_slope': trend_slope,
                'avg_changes': np.mean(change_counts),
                'trend_direction': 'increasing' if trend_slope > 0 else 'decreasing',
                'stability': 'stable' if abs(trend_slope) < 0.1 else 'unstable'
            }
        
        return {'trend': 'stable'}
    
    def reset_monitoring(self):
        """重置监控状态"""
        self.previous_state = None
        self.state_history.clear()
        self.change_statistics = {
            'total_changes': 0,
            'change_types': {},
            'change_frequency': []
        }
        self.logger.info("State monitoring reset")

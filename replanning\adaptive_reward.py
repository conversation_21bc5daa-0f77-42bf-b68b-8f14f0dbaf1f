"""
自适应奖励管理器

基于现有constellation_smp_reward函数，实现动态权重调整
支持多目标优化和情况自适应的奖励计算
"""

import torch
import torch.nn as nn
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
import logging

# 导入现有奖励函数
from constellation_smp.constellation_smp import reward as constellation_smp_reward


class AdaptiveRewardManager:
    """
    自适应奖励权重管理器
    
    功能:
    1. 基于现有奖励函数的扩展
    2. 动态权重调整
    3. 多目标平衡优化
    4. 情况自适应奖励计算
    """
    
    def __init__(self, constellation_mode: str = 'hybrid'):
        """
        初始化自适应奖励管理器
        
        Args:
            constellation_mode: 星座工作模式
        """
        self.constellation_mode = constellation_mode
        self.logger = logging.getLogger(__name__)
        
        # 基础权重配置
        self.base_weights = {
            'revenue': 0.4,           # 收益权重
            'resource_efficiency': 0.3, # 资源效率权重
            'load_balance': 0.2,      # 负载均衡权重
            'time_optimality': 0.1    # 时间最优性权重
        }
        
        # 动态调整历史
        self.weight_history = []
        self.performance_history = []
        self.max_history_length = 1000
        
        # 权重学习网络
        self.weight_network = self._build_weight_network()
        
        # 权重优化器
        self.weight_optimizer = torch.optim.Adam(
            self.weight_network.parameters(), lr=0.001
        )
        
        self.logger.info(f"AdaptiveRewardManager initialized for {constellation_mode} mode")
    
    def _build_weight_network(self) -> nn.Module:
        """构建权重学习网络"""
        return nn.Sequential(
            nn.Linear(8, 32),  # 输入：4个目标当前值 + 4个历史均值
            nn.ReLU(),
            nn.Linear(32, 16),
            nn.ReLU(),
            nn.Linear(16, 4),
            nn.Softmax(dim=-1)
        )
    
    def compute_adaptive_reward(self, static: torch.Tensor, tour_indices: torch.Tensor,
                               satellite_indices: torch.Tensor, constellation_mode: str,
                               current_situation: Optional[Dict[str, Any]] = None) -> Tuple[torch.Tensor, Dict[str, float], Dict[str, float]]:
        """
        计算自适应奖励
        
        Args:
            static: 静态数据
            tour_indices: 任务索引
            satellite_indices: 卫星索引
            constellation_mode: 星座模式
            current_situation: 当前情况描述
            
        Returns:
            Tuple: (自适应奖励, 奖励组件, 自适应权重)
        """
        try:
            # 确保所有张量在同一设备上
            device = static.device
            tour_indices = tour_indices.to(device)
            satellite_indices = satellite_indices.to(device)

            # 1. 计算基础奖励组件
            base_reward, revenue_rate, distance, memory, power = constellation_smp_reward(
                static, tour_indices, satellite_indices, constellation_mode
            )
            
            # 2. 分解奖励组件
            reward_components = self._decompose_reward_components(
                revenue_rate, distance, memory, power, static, tour_indices, satellite_indices
            )
            
            # 3. 计算自适应权重
            adaptive_weights = self._compute_adaptive_weights(
                reward_components, current_situation
            )
            
            # 4. 计算加权奖励
            adaptive_reward = self._compute_weighted_reward(
                reward_components, adaptive_weights
            )
            
            # 5. 记录历史
            self._record_performance(reward_components, adaptive_weights)
            
            return adaptive_reward, reward_components, adaptive_weights
            
        except Exception as e:
            self.logger.error(f"Adaptive reward computation failed: {e}")
            # 返回基础奖励作为备用
            base_reward, _, _, _, _ = constellation_smp_reward(
                static, tour_indices, satellite_indices, constellation_mode
            )
            return base_reward, {}, self.base_weights
    
    def _decompose_reward_components(self, revenue_rate: torch.Tensor, distance: torch.Tensor,
                                   memory: torch.Tensor, power: torch.Tensor,
                                   static: torch.Tensor, tour_indices: torch.Tensor,
                                   satellite_indices: torch.Tensor) -> Dict[str, float]:
        """分解奖励组件"""
        try:
            # 收益率组件
            revenue_component = revenue_rate.mean().item() if revenue_rate.numel() > 0 else 0.0
            
            # 资源效率组件
            resource_efficiency = 1.0 - (memory.mean().item() + power.mean().item()) / 2.0
            resource_efficiency = max(0.0, min(1.0, resource_efficiency))
            
            # 负载均衡组件
            load_balance = self._compute_load_balance(satellite_indices)
            
            # 时间最优性组件
            time_optimality = self._compute_time_optimality(static, tour_indices)
            
            return {
                'revenue': revenue_component,
                'resource_efficiency': resource_efficiency,
                'load_balance': load_balance,
                'time_optimality': time_optimality
            }
            
        except Exception as e:
            self.logger.error(f"Reward decomposition failed: {e}")
            return {
                'revenue': 0.0,
                'resource_efficiency': 0.0,
                'load_balance': 0.0,
                'time_optimality': 0.0
            }
    
    def _compute_load_balance(self, satellite_indices: torch.Tensor) -> float:
        """计算负载均衡度"""
        try:
            if satellite_indices.numel() == 0:
                return 0.0

            # 确保在CPU上计算
            satellite_indices_cpu = satellite_indices.cpu()

            # 统计每颗卫星的任务数量
            unique_sats, counts = torch.unique(satellite_indices_cpu, return_counts=True)

            if len(counts) <= 1:
                return 1.0  # 完美均衡（只有一颗卫星或所有任务分配给同一颗卫星）

            # 计算负载方差（越小越均衡）
            load_variance = torch.var(counts.float()).item()
            max_possible_variance = (len(counts) - 1) ** 2 / 4  # 理论最大方差

            # 归一化到[0,1]，1表示完美均衡
            balance_score = 1.0 - (load_variance / max_possible_variance) if max_possible_variance > 0 else 1.0
            return max(0.0, min(1.0, balance_score))
            
        except Exception as e:
            self.logger.error(f"Load balance computation failed: {e}")
            return 0.0
    
    def _compute_time_optimality(self, static: torch.Tensor, tour_indices: torch.Tensor) -> float:
        """计算时间最优性"""
        try:
            if static.numel() == 0 or tour_indices.numel() == 0:
                return 0.0
            
            # 基于静态数据中的时间信息计算时间最优性
            # static[:, 0, :]: task start time
            # static[:, 2, :]: task end time
            # static[:, 3, :]: time required for observation tasks
            
            batch_size = static.size(0)
            total_optimality = 0.0
            
            for b in range(batch_size):
                if b < tour_indices.size(0):
                    selected_tasks = tour_indices[b].cpu()  # 确保在CPU上
                    if selected_tasks.numel() > 0:
                        # 计算选中任务的时间效率
                        start_times = static[b, 0, selected_tasks]
                        end_times = static[b, 2, selected_tasks]
                        required_times = static[b, 3, selected_tasks]
                        
                        # 时间利用率
                        available_time = end_times - start_times
                        time_efficiency = required_times / (available_time + 1e-8)
                        total_optimality += torch.mean(torch.clamp(time_efficiency, 0, 1)).item()
            
            return total_optimality / batch_size if batch_size > 0 else 0.0
            
        except Exception as e:
            self.logger.error(f"Time optimality computation failed: {e}")
            return 0.0
    
    def _compute_adaptive_weights(self, reward_components: Dict[str, float],
                                 current_situation: Optional[Dict[str, Any]]) -> Dict[str, float]:
        """计算自适应权重"""
        try:
            # 构建输入特征
            current_values = torch.tensor([
                reward_components['revenue'],
                reward_components['resource_efficiency'],
                reward_components['load_balance'],
                reward_components['time_optimality']
            ], dtype=torch.float32)
            
            # 历史均值
            if len(self.performance_history) > 0:
                recent_history = self.performance_history[-10:]
                historical_means = torch.tensor([
                    np.mean([h['revenue'] for h in recent_history]),
                    np.mean([h['resource_efficiency'] for h in recent_history]),
                    np.mean([h['load_balance'] for h in recent_history]),
                    np.mean([h['time_optimality'] for h in recent_history])
                ], dtype=torch.float32)
            else:
                historical_means = current_values.clone()
            
            # 网络输入
            network_input = torch.cat([current_values, historical_means])
            
            # 计算自适应权重
            with torch.no_grad():
                adaptive_weights_tensor = self.weight_network(network_input)
            
            # 转换为字典格式
            weight_keys = ['revenue', 'resource_efficiency', 'load_balance', 'time_optimality']
            adaptive_weights = {
                key: adaptive_weights_tensor[i].item()
                for i, key in enumerate(weight_keys)
            }
            
            # 根据情况类型进行特殊调整
            if current_situation:
                adaptive_weights = self._adjust_weights_by_situation(
                    adaptive_weights, current_situation
                )
            
            return adaptive_weights
            
        except Exception as e:
            self.logger.error(f"Adaptive weight computation failed: {e}")
            return self.base_weights.copy()
    
    def _adjust_weights_by_situation(self, weights: Dict[str, float],
                                   situation: Dict[str, Any]) -> Dict[str, float]:
        """根据情况调整权重"""
        adjusted_weights = weights.copy()
        
        situation_type = situation.get('type', '')
        
        if situation_type == 'satellite_failure':
            # 卫星故障时，提高负载均衡权重
            adjusted_weights['load_balance'] *= 1.5
        elif situation_type == 'resource_constraint':
            # 资源约束时，提高资源效率权重
            adjusted_weights['resource_efficiency'] *= 1.3
        elif situation_type == 'priority_update':
            # 优先级更新时，提高收益权重
            adjusted_weights['revenue'] *= 1.2
        elif situation_type == 'time_critical':
            # 时间紧急时，提高时间最优性权重
            adjusted_weights['time_optimality'] *= 1.4
        
        # 重新归一化
        return self._normalize_weights(adjusted_weights)
    
    def _normalize_weights(self, weights: Dict[str, float]) -> Dict[str, float]:
        """归一化权重"""
        total = sum(weights.values())
        if total > 0:
            return {key: value / total for key, value in weights.items()}
        else:
            return self.base_weights.copy()
    
    def _compute_weighted_reward(self, reward_components: Dict[str, float],
                               weights: Dict[str, float]) -> torch.Tensor:
        """计算加权奖励"""
        weighted_reward = sum(
            weights.get(key, 0) * reward_components.get(key, 0)
            for key in weights.keys()
        )
        return torch.tensor(weighted_reward, dtype=torch.float32)
    
    def _record_performance(self, reward_components: Dict[str, float],
                          weights: Dict[str, float]):
        """记录性能历史"""
        record = {
            'timestamp': len(self.performance_history),
            **reward_components,
            'weights': weights.copy()
        }
        
        self.performance_history.append(record)
        self.weight_history.append(weights.copy())
        
        # 限制历史长度
        if len(self.performance_history) > self.max_history_length:
            self.performance_history.pop(0)
        if len(self.weight_history) > self.max_history_length:
            self.weight_history.pop(0)
    
    def update_weight_network(self, target_performance: Dict[str, float]):
        """更新权重网络"""
        try:
            if len(self.performance_history) < 10:
                return
            
            # 构建训练数据
            recent_history = self.performance_history[-10:]
            
            for record in recent_history:
                # 输入特征
                current_values = torch.tensor([
                    record['revenue'],
                    record['resource_efficiency'],
                    record['load_balance'],
                    record['time_optimality']
                ], dtype=torch.float32)
                
                historical_means = current_values  # 简化处理
                network_input = torch.cat([current_values, historical_means])
                
                # 预测权重
                predicted_weights = self.weight_network(network_input)
                
                # 目标权重（基于性能反馈调整）
                target_weights = torch.tensor([
                    target_performance.get('revenue', 0.4),
                    target_performance.get('resource_efficiency', 0.3),
                    target_performance.get('load_balance', 0.2),
                    target_performance.get('time_optimality', 0.1)
                ], dtype=torch.float32)
                
                # 计算损失
                loss = nn.MSELoss()(predicted_weights, target_weights)
                
                # 反向传播
                self.weight_optimizer.zero_grad()
                loss.backward()
                self.weight_optimizer.step()
                
        except Exception as e:
            self.logger.error(f"Weight network update failed: {e}")
    
    def get_weight_statistics(self) -> Dict[str, Any]:
        """获取权重统计信息"""
        if not self.weight_history:
            return {'status': 'No weight history'}
        
        recent_weights = self.weight_history[-10:]
        
        stats = {}
        for key in self.base_weights.keys():
            values = [w.get(key, 0) for w in recent_weights]
            stats[f'{key}_mean'] = np.mean(values)
            stats[f'{key}_std'] = np.std(values)
        
        return stats
